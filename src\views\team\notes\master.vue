<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick" >
      <el-tab-pane label="(三班)班组记事" name="threeClass">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="业务日期" prop="createTime">
            <el-date-picker style="margin-left: 10px;width: 392px;"
                            v-model="createTimeArr" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="班组名称" prop="workGroupName">
            <el-select v-model="queryParams.workGroupName" placeholder="请选择">
              <el-option
                v-for="dict in dict.type.common_workGroup" :key="dict.value" :label="dict.label" :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="班别名称" prop="workClassName">
            <el-select v-model="queryParams.workClassName" placeholder="请选择">
              <el-option
                v-for="dict in dict.type.common_workClass" :key="dict.value" :label="dict.label" :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="工长" prop="chargeHand">
            <el-input
              v-model="queryParams.chargeHand"
              placeholder="请输入工长"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <div class="mes_table">
          <vxe-table
            border
            :edit-config="{trigger: 'click', mode: 'row'}"
            @edit-closed="editClosedEvent"
            :data="tableData"
            :checkbox-config="{checkField: 'isChecked', indeterminateField: 'isIndeterminate'}"
            @checkbox-change="handleCheckboxChange">
            >
            <vxe-column type="checkbox"  width="60" fixed="left"></vxe-column>
<!--            :edit-render="{ name: 'VxeDatePicker', props: { type: 'datetime' } }"-->
            <vxe-column field="workDate" title="业务日期"  width="150" fixed="left"></vxe-column>
            <!-- <vxe-column field="prodCenterCode" title="加工中心编码" width="120" :header-align="'center'" fixed="left"></vxe-column> -->
            <vxe-column field="prodCenterName" title="加工中心名称" width="120" :header-align="'center'" fixed="left"></vxe-column>
            <vxe-column field="workGroupName" title="班组" :edit-render="{name: 'input'}" width="80" :header-align="'center'" fixed="left"></vxe-column>
            <vxe-column field="workClassName" title="班别" :edit-render="{name: 'input'}" width="80" :header-align="'center'" fixed="left"></vxe-column>
            <vxe-column field="chargeHand" title="工长" :edit-render="{name: 'input'}" width="80" :header-align="'center'"></vxe-column>
            <vxe-column field="assistantChargeHand" title="副工长" :edit-render="{name: 'input'}" width="90" :header-align="'center'"></vxe-column>
            <vxe-column field="recordInfo" title="记录信息" :edit-render="{name: 'input'}" width="120" :header-align="'center'"></vxe-column>
            <vxe-column field="returnMine" title="返矿"width="80" :header-align="'center'" :edit-render="{name: 'input', attrs: { type: 'number' }}"></vxe-column>
            <vxe-column field="returnFocal" title="返焦" width="80" :header-align="'center'" :edit-render="{name: 'input', attrs: { type: 'number' }}"></vxe-column>
            <vxe-column field="gunBlockNum" title="堵枪次数" :edit-render="{name: 'input', attrs: { type: 'number' }}"width="100" :header-align="'center'"></vxe-column>
            <vxe-column field="ironReduction" title="化铁" :edit-render="{name: 'input'}"width="80" :header-align="'center'"></vxe-column>
            <vxe-column field="greyOut" title="放灰" :edit-render="{name: 'input', attrs: { type: 'number' }}"width="80" :header-align="'center'"></vxe-column>
            <vxe-column field="greyOutTime" title="放灰时间"width="100" :header-align="'center'"></vxe-column>
            <vxe-column field="returnAcidSieve" title="返酸筛下" :edit-render="{name: 'input'}"width="100"></vxe-column>
            <vxe-colgroup title="T/H" :header-align="'center'">
              <vxe-column field="thName2_2" title="2#" width="auto" v-if="this.getProdCemterCode() == 'IPES02'"></vxe-column>
              <vxe-column field="thName2_3" title="3#" width="auto" v-if="this.getProdCemterCode() == 'IPES02'"></vxe-column>
              <vxe-column field="thName2_4" title="4#" width="auto" v-if="this.getProdCemterCode() == 'IPES02'"></vxe-column>
              <vxe-column field="thName2_5" title="5#" width="auto" v-if="this.getProdCemterCode() == 'IPES02'"></vxe-column>
              <vxe-column field="thName2_6" title="6#" width="auto" v-if="this.getProdCemterCode() == 'IPES02'"></vxe-column>
              <vxe-column field="thName2_7" title="7#" width="auto" v-if="this.getProdCemterCode() == 'IPES02'"></vxe-column>

              <vxe-column field="thName1_2" title="2#" width="auto" v-if="this.getProdCemterCode() == 'IPES01'"></vxe-column>
              <vxe-column field="thName1_3" title="3#" width="auto" v-if="this.getProdCemterCode() == 'IPES01'"></vxe-column>
              <vxe-column field="thName1_4" title="4#" width="auto" v-if="this.getProdCemterCode() == 'IPES01'"></vxe-column>
              <vxe-column field="thName1_5" title="5#" width="auto" v-if="this.getProdCemterCode() == 'IPES01'"></vxe-column>
              <vxe-column field="thName1_6" title="6#" width="auto" v-if="this.getProdCemterCode() == 'IPES01'"></vxe-column>
            </vxe-colgroup>
            <vxe-column field="createBy" title="创建人"width="80" :header-align="'center'"></vxe-column>
            <vxe-column field="createTime" title="创建时间"width="150" :header-align="'center'"></vxe-column>
            <vxe-column field="updateBy" title="更新人"width="80" :header-align="'center'"></vxe-column>
            <vxe-column field="updateTime" title="更新时间"width="150" :header-align="'center'"></vxe-column>
          </vxe-table>
          <vxe-pager
            :current-page.sync="mainTableConfig.pageConfig.pageNum"
            :page-size.sync="mainTableConfig.pageConfig.pageSize"
            :total="mainTableConfig.pageConfig.total"
            @page-change="pageChange">
          </vxe-pager>
        </div>

        <!-- 添加或修改班组记事主对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="业务日期" prop="workDate">
              <el-date-picker v-model="form.workDate" type="datetime" placeholder="选择日期时间"></el-date-picker>
            </el-form-item>
            <el-form-item label="备注" prop="remark" class="addItemModel">
              <el-input v-model="form.remark" placeholder="请输入备注"/>
            </el-form-item>
            <el-form-item label="班组名称" prop="workGroupName">
              <el-select v-model="form.workGroupName" placeholder="请选择" style="width: 220px;">
                <el-option
                  v-for="dict in dict.type.common_workGroup" :key="dict.value" :label="dict.label" :value="dict.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="班别名称" prop="workClassName" class="addItemModel">
              <el-select v-model="form.workClassName" placeholder="请选择" style="width: 220px;">
                <el-option
                  v-for="dict in dict.type.common_workClass" :key="dict.value" :label="dict.label" :value="dict.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="工长" prop="chargeHand" style="width: 300px;">
              <el-input v-model="form.chargeHand" placeholder="请输入工长"/>
            </el-form-item>
            <el-form-item label="副工长" prop="assistantChargeHand" class="addItemModel">
              <el-input v-model="form.assistantChargeHand" placeholder="请输入副工长"/>
            </el-form-item>
            <el-form-item label="记录信息" prop="recordInfo">
              <el-input autosize  type="textarea" v-model="form.recordInfo" placeholder="请输入记录信息" style="width: 538px;"/>
            </el-form-item>
            <el-form-item label="堵枪次数" prop="gunBlockNum" style="width: 300px;">
              <el-input v-model="form.gunBlockNum" placeholder="请输入堵枪次数"/>
            </el-form-item>
            <el-form-item label="化铁(斗)" prop="ironReduction" class="addItemModel">
              <el-input v-model="form.ironReduction" placeholder="请输入化铁"/>
            </el-form-item>
            <el-form-item label="放灰(车)" prop="greyOut" style="width: 300px;">
              <el-input v-model="form.greyOut" placeholder="请输入放灰"/>
            </el-form-item>
            <el-form-item label="返酸筛下" prop="returnAcidSieve" class="addItemModel">
              <el-input v-model="form.returnAcidSieve" placeholder="请输入返酸筛下"/>
            </el-form-item>
            <el-form-item label="放灰时间" prop="greyOutTime">
              <el-date-picker clearable  v-model="greyOutTimeArr" type="datetimerange"
                              range-separator="至"
                              start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-tab-pane>
      <el-tab-pane label="(日)班组记事" name="dayClass">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="业务日期" prop="createTime">
            <el-date-picker style="margin-left: 10px;width: 392px;"
                            v-model="createTimeDayArr" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleDayQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetDayQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="mes_table">
          <vxe-table
            border
            :edit-config="{trigger: 'click', mode: 'row'}"
            @edit-closed="editClosedEvent"
            :data="tableData"
            :checkbox-config="{checkField: 'isChecked', indeterminateField: 'isIndeterminate'}"
            @checkbox-change="handleCheckboxChange">
            >
            <vxe-column type="checkbox"  width="60" fixed="left"></vxe-column>
            <vxe-column field="workDate" title="业务日期" :edit-render="{ name: 'VxeDatePicker', props: { type: 'datetime' } }" width="150" fixed="left"></vxe-column>
            <!-- <vxe-column field="prodCenterCode" title="加工中心编码" width="120" :header-align="'center'" fixed="left"></vxe-column> -->
            <vxe-column field="prodCenterName" title="加工中心名称" width="120" :header-align="'center'" fixed="left"></vxe-column>
            <vxe-column field="returnMine" title="返矿"width="80" :header-align="'center'" ></vxe-column>
            <vxe-column field="returnFocal" title="返焦" width="80" :header-align="'center'" ></vxe-column>
            <vxe-column field="gunBlockNum" title="堵枪次数" width="100" :header-align="'center'"></vxe-column>
            <vxe-column field="ironReduction" title="化铁" width="80" :header-align="'center'"></vxe-column>
            <vxe-column field="greyOut" title="放灰" width="80" :header-align="'center'"></vxe-column>
            <vxe-column field="dustAshBurnedOut" title="除尘灰烧损" :edit-render="{name: 'input', attrs: { type: 'number' }}"width="130" v-if="this.getProdCemterCode() == 'IPES01' && this.showInput == true" ></vxe-column>
            <vxe-column field="furnaceGrade" title="入炉品位" :edit-render="{name: 'input', attrs: { type: 'number' }}"width="100" v-if="this.getProdCemterCode() == 'IPES01' && this.showInput == true" ></vxe-column>
            <vxe-column field="theoreticalIron" title="理论铁量" :edit-render="{name: 'input', attrs: { type: 'number' }}"width="100" v-if="this.getProdCemterCode() == 'IPES01' && this.showInput == true" ></vxe-column>
            <vxe-column field="insertCokeCubes" title="插焦丁" :edit-render="{name: 'input', attrs: { type: 'number' }}"width="100" v-if="this.getProdCemterCode() == 'IPES02' && this.showInput == true"></vxe-column>
            <vxe-column field="cokeQuantity" title="焦炭量" :edit-render="{name: 'input', attrs: { type: 'number' }}"width="100" v-if="this.getProdCemterCode() == 'IPES02' && this.showInput == true" ></vxe-column>
            <vxe-column field="selfTestCoalPowder_200" title="自测煤粉-200" :edit-render="{name: 'input', attrs: { type: 'number' }}"width="130" v-if="this.getProdCemterCode() == 'IPES02' && this.showInput == true" ></vxe-column>

            <vxe-column field="createBy" title="创建人"width="80" :header-align="'center'"></vxe-column>
            <vxe-column field="createTime" title="创建时间"width="150" :header-align="'center'"></vxe-column>
            <vxe-column field="updateBy" title="更新人"width="80" :header-align="'center'"></vxe-column>
            <vxe-column field="updateTime" title="更新时间"width="150" :header-align="'center'"></vxe-column>
          </vxe-table>
          <vxe-pager
            :current-page.sync="mainTableConfigDay.pageConfig.pageNum"
            :page-size.sync="mainTableConfigDay.pageConfig.pageSize"
            :total="mainTableConfigDay.pageConfig.total"
            @page-change="pageChangeDay">
          </vxe-pager>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import {
    listMaster,
    addMaster,
    editMaster,
    delMaster,
    listMasterDay,
  } from "@/api/teamnotes/master";
  import dayjs from "dayjs";

  export default {
    name: "Master",
    dicts: [
      'common_workGroup',
      'common_workClass',
    ],
    data() {
      return {
        activeName: 'threeClass',
        showInput: true,
        mainTableConfig: {
          tableData: [],
          selectVO: '',
          pageConfig: {
            pageNum: 1, // 页码
            pageSize: 10, // 每页显示条目个数
            total: 0, // 总数
            background: true, // 是否展示分页器背景色
            pageSizes: [10, 20, 50, 100]// 分页器分页待选项
          }
        },
        mainTableConfigDay: {
          tableData: [],
          selectVO: '',
          pageConfig: {
            pageNum: 1, // 页码
            pageSize: 10, // 每页显示条目个数
            total: 0, // 总数
            background: true, // 是否展示分页器背景色
            pageSizes: [10, 20, 50, 100]// 分页器分页待选项
          }
        },
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 班组记事主表格数据
        masterList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNums: 1,
          pageSizes: 10,
          prodCenterCode: null,
          prodCenterName: null,
          workGroupName: null,
          workClassName: null,
          chargeHand: null,
          assistantChargeHand: null,
          recordInfo: null,
          remark: null,
          operator: null,
          operatTime: null,
          createBy: null,
          createTime: null,
          updateBy: null,
          updateTime: null,
          role: null,
          createTimeStart:null,
          createTimeEnd:null,
          reportUnitCode:null,
        },
        // 更换日期 插件
        createTimeArr:[],
        createTimeDayArr:[],
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          prodCenterCode: [
            {
              required: true, message: "加工中心编码不能为空", trigger: "blur" }
          ],
          workGroupName: [
            {
              required: true, message: "班组名称不能为空", trigger: "blur" }
          ],
        },
        tableData: [],
        selectVO: '',
        // 放灰时间
        greyOutTimeArr:[],
      };
    },
    created() {
      this.createTimeArr.push(dayjs(new Date()).add(-7, "day"));
      this.createTimeArr.push(dayjs(new Date()));
      this.getList(null);
      // 日 班组日期
      this.createTimeDayArr.push(dayjs(new Date()).add(-7, "day"));
      this.createTimeDayArr.push(dayjs(new Date()));
      // this.getDayList(null);


    },
    methods: {
      /*  获取加工中心编码  */
      getProdCemterCode() {
        return this.$route.query.prodCenterCode;
      },
      /*  获取角色  */
      getRole() {
        return this.$route.query.role;
      },


      /** 查询班组记事主列表 */
      getList(selectVO) {
        this.loading = true;
        if (selectVO) {
          this.selectVO = selectVO;
        }
        this.queryList();
      },
      queryList() {
        if(this.createTimeArr != null){
          if (this.createTimeArr.length == 2) {
            this.queryParams.workDateStart = dayjs(this.createTimeArr[0]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.queryParams.workDateEnd = dayjs(this.createTimeArr[1]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }
        }
        this.queryParams.prodCenterCode = this.getProdCemterCode();
        // if(this.activeName="threeClass"){
          this.queryParams.role = this.getRole();
        // }
        // else if(this.activeName="dayClass"){
        //   this.queryParams.role = "日";
        // }
        listMaster(this.queryParams, this.selectVO).then(response => {
          this.tableData = response.data
          console.log("this.tableData:",JSON.stringify(this.tableData))
          this.mainTableConfig.pageConfig.total = response.total
          this.loading = false;
        });
      },
      pageChange ({ pageSize, currentPage }) {
        this.mainTableConfig.pageConfig.pageNum = currentPage
        this.mainTableConfig.pageConfig.pageSize = pageSize
        this.queryParams.pageNum=this.mainTableConfig.pageConfig.pageNum
        this.queryParams.pageSize=this.mainTableConfig.pageConfig.pageSize
        this.queryList()
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          fid: null,
          prodCenterCode: null,
          prodCenterName: null,
          workGroupName: null,
          workClassName: null,
          chargeHand: null,
          assistantChargeHand: null,
          recordInfo: null,
          remark: null,
          operator: null,
          operatTime: null,
          createBy: null,
          createTime: null,
          updateBy: null,
          updateTime: null,
          role: null,
          gunBlockNum: null,
          ironReduction: null,
          greyOut: null,
          returnAcidSieve: null,
          greyOutStartTime: null,
          greyOutEndTime: null,
          workDateStart:null,
          workDateEnd:null,
        };
        this.greyOutTimeArr = null;
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        if(this.createTimeArr != null){
          if (this.createTimeArr.length == 2) {
            this.queryParams.workDateStart = dayjs(this.createTimeArr[0]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.queryParams.workDateEnd = dayjs(this.createTimeArr[1]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }
        }
        this.queryParams.pageNum = 1;
        this.getList();
      },

      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },

      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加班组记事";
        this.form.workDate = dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
      },

      // getCurrentTimeString() {
      //     const now = new Date();
      //     const year = now.getFullYear();
      //     const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份补零
      //     const day = String(now.getDate()).padStart(2, '0');
      //     const hours = String(now.getHours()).padStart(2, '0');
      //     const minutes = String(now.getMinutes()).padStart(2, '0');
      //     const seconds = String(now.getSeconds()).padStart(2, '0');
      //     return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      // },

      /** 提交按钮 */
      submitForm() {
        console.log("this.greyOutTimeArr:",this.greyOutTimeArr)
        if(this.greyOutTimeArr != null) {
          if (this.greyOutTimeArr.length == 2) {
            this.form.greyOutStartTime = dayjs(this.greyOutTimeArr[0]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.form.greyOutEndTime = dayjs(this.greyOutTimeArr[1]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }
        }
        this.form.prodCenterCode = this.getProdCemterCode();
        this.form.role = this.getRole();
        this.form.operatTime = dayjs(this.form.operatTime).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        let tTeamNotesDetail={};
        tTeamNotesDetail.gunBlockNum = this.form.gunBlockNum // 堵枪次数
        tTeamNotesDetail.ironReduction = this.form.ironReduction // 化铁
        tTeamNotesDetail.greyOut = this.form.greyOut // 化铁
        tTeamNotesDetail.returnAcidSieve = this.form.returnAcidSieve // 返酸筛下
        tTeamNotesDetail.greyOutStartTime = this.form.greyOutStartTime
        tTeamNotesDetail.greyOutEndTime = this.form.greyOutEndTime
        let combinedObj = {...this.form, ...tTeamNotesDetail}
        addMaster(combinedObj).then(response=>{
          this.$modal.msgSuccess("添加成功");
          this.open = false;
          this.getList();
          this.reset();
        });


      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const id = row.id || this.ids
        this.$modal.confirm('确定删除选中的数据吗？').then(function () {
          console.log("id:",JSON.stringify(id))
          return delMaster(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => { });

      },
      /** 导出按钮操作 */
      handleExport()
      {
        this.download('api/teamNotes/master/export', {
          ...this.queryParams
        }, `master_${new Date().getTime()}.xlsx`)
      },
      /*关闭编辑框触发 失去焦点 进行保存数据*/
      editClosedEvent(row, column) {
        console.log("row.row:",JSON.stringify(row.row))

        editMaster(row.row).then(response=>{
          this.$modal.msgSuccess("编辑成功");
        });

      },
      /*多选框触发事件*/
      handleCheckboxChange({ records, rowIndex, row }) {
        console.log("records:",records)
        this.ids = records.map(item => item.fid)
        console.log("this.ids:",this.ids)
        this.single = records.length !== 1
        this.multiple = !records.length
      },
      /////////////////////// 以下是日的班组记事触发条件  ///////////////////////////////////////
      /**(日) 搜索按钮操作 */
      handleDayQuery(){
        this.queryParams.greyOutStartTime=null;
        this.queryParams.greyOutEndTime=null;
        if(this.createTimeDayArr != []){
          if (this.createTimeDayArr.length == 2) {
            this.queryParams.workDateStart = dayjs(this.createTimeDayArr[0]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.queryParams.workDateEnd = dayjs(this.createTimeDayArr[1]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }
        }

        this.getDayList(null);
      },
      /** 查询班组记事主列表 */
      getDayList(selectVO) {
        this.loading = true;
        if (selectVO) {
          this.selectVO = selectVO;
        }
        this.queryDayList();
      },
      queryDayList() {
        if(this.createTimeDayArr != []){
          if (this.createTimeDayArr.length == 2) {
            this.queryParams.workDateStart = dayjs(this.createTimeDayArr[0]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.queryParams.workDateEnd = dayjs(this.createTimeDayArr[1]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }
        }
        this.queryParams.prodCenterCode = this.getProdCemterCode();
        if(this.activeName="threeClass"){
          this.queryParams.role = this.getRole();
        }
        listMasterDay(this.queryParams, this.selectVO).then(response => {
          this.tableData = response.rows
          console.log("this.tableData1111:",JSON.stringify(this.tableData))
          this.mainTableConfig.pageConfig.total = response.total
          this.loading = false;
        });
      },
      pageChangeDay ({ pageSize, currentPage }) {
        this.mainTableConfigDay.pageConfig.pageNum = currentPage
        this.mainTableConfigDay.pageConfig.pageSize = pageSize
        this.queryParams.pageNum=this.mainTableConfigDay.pageConfig.pageNum
        this.queryParams.pageSize=this.mainTableConfigDay.pageConfig.pageSize
        this.queryDayList()
      },
      /**(日) 重置按钮操作 */
      resetDayQuery(){

      },

      /* tab页*/
      handleClick(tab, event) {
        this.activeName = tab.name
        console.log("11"+tab); // 打印完整的 tab 对象，包括 name, label 等属性
        console.log("22"+tab.name); // 打印当前 tab 的 name 属性
        console.log("33"+tab.label)
        if(this.activeName == "threeClass"){
          console.log("threeClassthreeClass")
          this.getList(null);
        }
        else if(this.activeName == "dayClass"){
          console.log("dayClass dayClass")
          this.getDayList(null);
        }

      },

    }
  }
  ;
</script>

<style lang="scss"  scoped>
  /*新增弹框 班别名称*/
  .addItemModel{
    width: 300px;
    z-index: auto;
    position: fixed;
    margin-top: -62px;
    margin-left: 318px;
  }
</style>
