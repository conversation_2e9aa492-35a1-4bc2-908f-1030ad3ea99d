<template>
  <div>
      <vxe-grid v-bind="gridOptions" style="margin: 10px;" @page-change="pageChangeEvent">
          <template #form>
              <vxe-form ref="formRef" v-bind="formOptions">
                  <template #action>
                      <vxe-button status="primary" @click="searchEvent">搜索</vxe-button>
                      <vxe-button @click="resetEvent">重置</vxe-button>
                  </template>
              </vxe-form>
          </template>
      </vxe-grid>
  </div>
</template>

<script>
import { selectStockoutData } from "@/api/wms/stockinout";
import { initMaterialList } from "@/api/md/material";

export default {
  name: 'selectStockoutData',

  data() {
      const materialRender = {
          name: 'VxeSelect',
          props: {
              filterable: true,
              clearable: true,
          },
          options: []
      }
      const materialsRender = {
          name: 'VxeSelect',
          props: {
              filterable: true,
              clearable: true,
              multiple: true,
          },
          options: []
      }
      return {
          gridOptions: {
              columns: [],
              data: [],

              border: true,
              stripe: true,
              align: 'center',
              height: 800,

              columnConfig: {
                  resizable: true
              },
              rowConfig: {
                  isHover: true,
                  isCurrent: true,
              },

              mouseConfig: {
                  selected: true
              },

              pagerConfig: {
                  total: 0,
                  currentPage: 1,
                  pageSize: 10
              },

              toolbarConfig: {
                  custom: true,
                  zoom: true,
                  // slots: {
                  //     buttons: 'toolbarButtons'
                  // }
              },

              // editConfig: {
              //     trigger: 'dblclick',
              //     mode: 'cell',
              // },

              // toolbarConfig: {
              //     buttons: [
              //         { name: '新增', code: 'myAdd', status: 'primary' },
              //         { name: '删除', code: 'myDel', status: 'error' },
              //         { name: '保存', code: 'mySave', status: 'success' }
              //     ]
              // },

              // mergeCells: [
              //     { row: 0, col: 1, rowspan: 3 },
              //     { row: 3, col: 1, rowspan: 3 },
              // ],
          },
          formOptions: {
              data: {
                  beginTime: new Date().toISOString().split('T')[0] + ' 00:00:00',
                  endTime: new Date().toISOString().split('T')[0] + ' 23:59:59',
                  mateName: '',
                  stockInType: ''
              },
              items: [
                  { field: 'beginTime', title: '开始时间', itemRender: { name: 'VxeDatePicker', props: { type: 'datetime' } } },
                  { field: 'endTime', title: '结束时间', itemRender: { name: 'VxeDatePicker', props: { type: 'datetime' } } },
                  { field: 'mateCode', title: '物料名称', itemRender: materialRender },
                  // { field: 'stockInType', title: '入库类型', itemRender: { name: 'VxeInput' } },
                  { slots: { default: 'action' } }
              ]
          },
          materialRender,
          materialsRender,
      }
  },

  mounted() {
      this.initGridData();
      this.initData();
  },

  methods: {
      initData() {
          initMaterialList().then(response => {
              let data = response.data
              let list = []
              for (let i = 0; i < data.length; i++) {
                  list.push({
                      value: `${data[i].materialNumber}`,
                      label: `${data[i].shortName ? data[i].shortName : data[i].materialName}` + `(${data[i].materialNumber})`
                  })
              }
              this.materialRender.options = list
              this.materialsRender.options = list
          })
      },
      initGridData() {
          // selectStockinoutData().then(response => {
          //     this.gridOptions.data = response.data;
          // });
          this.gridOptions.columns = [
              { type: 'seq', width: 50 },
              { field: 'operType', title: '出库类型', },
              { field: 'mateCode', title: '物料编码', },
              { field: 'mateName', title: '物料名称', },
              { field: 'specCode', title: '规格型号', },
              { field: 'stockLot', title: '船号/厂家', },
              { field: 'sendStoreHouseName', title: '发货仓库', },
              { field: 'reveiveStoreHouseName', title: '收货仓库', },
              { field: 'weight', title: '净重', },
              { field: 'OPPOSITE_THEORETICAL_WEIGHT', title: '对方重量', },
              { field: 'ORDER_NUMBER', title: '核心单据号', },
              { field: 'souceCode', title: '检斤单', },
              { field: 'carNumber', title: '车牌号', },
              // { field: 'stockWeight', title: '库存重量', editRender: { name: 'VxeNumberInput', autoSelect: true } },
          ]
          this.handlePageData()
      },

      async searchEvent() {
          this.handlePageData()
      },

      resetEvent() {
          const $form = this.$refs.formRef
          if ($form) {
              $form.reset()
              this.handlePageData()
          }
      },

      handlePageData() {
          this.gridOptions.loading = true
          this.formOptions.data.sendStoreHouseCode = this.getStoreHouseCode()
          selectStockoutData(this.formOptions.data).then(response => {
              let data = response.data
              const { pageSize, currentPage } = this.gridOptions.pagerConfig
              this.gridOptions.pagerConfig.total = data.length
              this.gridOptions.data = data.slice((currentPage - 1) * pageSize, currentPage * pageSize)
              this.gridOptions.loading = false

          })
      },

      pageChangeEvent({ pageSize, currentPage }) {
          this.gridOptions.pagerConfig.currentPage = currentPage
          this.gridOptions.pagerConfig.pageSize = pageSize
          this.handlePageData()
      },

      getProdCenterCode() {
          return this.$route.query.prodCenterCode
      },

      getStoreHouseCode() {
          return this.$route.query.storehouseCode
      },
  }
}
</script>
