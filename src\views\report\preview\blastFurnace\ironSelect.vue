<template>
  <div class="app-container" style="padding: 0px;">
      <div class="block" style="margin-top:8px;margin-left: 20px;">
        <span style=" margin-left: 10px;">业务日期:</span>
          <el-date-picker style="margin-left: 10px;width: 350px;"
                          v-model="createTimeArr" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
        <span style=" margin-left: 10px;">罐任务号:</span>
        <el-input v-model="selectParam.canTaskNumber" placeholder="请输入内容"  style="width: 200px;margin-left: 10px;"  clearable></el-input>
        <span style=" margin-left: 10px;">是否到达倒灌站:</span>
        <el-select v-model="selectParam.backCanArriveFlag" placeholder="请选择" clearable style="width: 200px;margin-left: 10px;" >
          <el-option v-for="item in backCanFalgList" :key="item.value" :label="item.label" :value="item.value" ></el-option>
        </el-select>
        <span style=" margin-left: 10px;">高炉号:</span>
        <el-select v-model="selectParam.staveNo" placeholder="请选择" clearable style="width: 200px;margin-left: 10px;" >
          <el-option v-for="item in staveNoList" :key="item.value" :label="item.label" :value="item.value" ></el-option>
        </el-select>
        <el-button type="primary" style="margin-left: 30px;" @click="handleQuery" size="mini">搜索</el-button>
        <el-button type="primary" style="margin-left: 30px;" @click="clickMixedCan" size="mini">混罐</el-button>
        <el-button type="primary" style="margin-left: 30px;" @click="clickSave" size="mini">保存</el-button>

      </div>
    <div style="margin-top: 7px;margin-left: 20px;margin-right: 20px;">
      <vxe-table
        border
        :loading="loading"
        ref="tableRef"
        :edit-config="{trigger: 'click', mode: 'row'}"
        @edit-closed="editClosedEvent"
        :checkbox-config="{checkField: 'isChecked', indeterminateField: 'isIndeterminate'}"
        @checkbox-change="handleCheckboxChange"
        @cell-click="handleCellClickTime"
        :data="tableData">
        <vxe-column type="checkbox" width="60"></vxe-column>
        <vxe-column field="businessTime" title="业务日期" :edit-render="{ name: 'VxeDatePicker', props: { type: 'datetime' } }" width="150" ></vxe-column>
        <vxe-column field="ironNumber" title="铁次号"  width='auto'></vxe-column>
        <vxe-column field="ironSlogan1" title="铁口"  width='auto' ></vxe-column>
        <vxe-column field="canTaskNumber" title="罐任务号"  width='auto'></vxe-column>
        <vxe-column field="canNumber1" title="罐号"  width='auto' ></vxe-column>
        <vxe-column field="mixedCan" title="混罐号"  width='auto' ></vxe-column>
        <vxe-column field="estimateWeight1" title="估重"  width='auto' ></vxe-column>
        <vxe-column field="highestPhtsicalHeat" title="铁水温度"  width='auto'></vxe-column>
        <vxe-column field="canStatus1" title="鱼雷罐状态"  width='auto':edit-render="canStatusEditRender"></vxe-column>
        <vxe-column field="steelScrapAmount1" title="废钢加入量(吨)" width='auto' ></vxe-column>
        <vxe-column field="backCanStationArriveTime" title="倒灌站到达时间" width='auto' ></vxe-column>
      </vxe-table>
          <vxe-pager
            :current-page.sync="mainTableConfig.pageConfig.pageNum"
            :page-size.sync="mainTableConfig.pageConfig.pageSize"
            :total="mainTableConfig.pageConfig.total"
            @page-change="pageChange">
          </vxe-pager>
    </div>
  </div>

</template>

<script>
  import {
    listIronSelect,
    mixedCanMethod,
    ironCanWeightInputEdit,
  } from "@/api/report/preview/blastFurnace/ironSelect";
  import {
    ironWeightInputEdit,
  } from "@/api/report/preview/blastFurnace/input";
    import dayjs from "dayjs";
    export default {
        name: "ironSelect",
      data() {
          return{
            // 加载
            loading:true,
            // 鱼雷罐状态
            canStatusEditRender : {
              name: 'VxeSelect',
              options: [
                { label: '冷罐/修补罐', value: '冷罐/修补罐' },
                { label: '新罐', value: '新罐' },
                { label: '周转罐', value: '周转罐' }
              ]
            },
            mainTableConfig: {
              tableData: [],
              selectVO: '',
              pageConfig: {
                pageNum: 1, // 页码
                pageSize: 10, // 每页显示条目个数
                total: 0, // 总数
                background: true, // 是否展示分页器背景色
                pageSizes: [10, 20, 50, 100]// 分页器分页待选项
              }
            },
            selectParam:{
              canTaskNumber:'',
              backCanArriveFlag:'',
              staveNo:'',
            },
            backCanFalgList: [{
              value: '已到达',
              label: '已到达'
            }, {
              value: '未到达',
              label: '未到达'
            }],
            staveNoList:[{
              value: '1',
              label: '1高炉'
            }, {
              value: '2',
              label: '2高炉'
            }],
            tableData:[],
            // 混罐使用
            canTaskNumberList:[],
            ironCanWeightInputClose:{},
            createTimeArr:[],
          }
      },
      created(){
        const now = new Date();
        // 获取当天 0点
        this.startOfDay = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate(),
          0, 0, 0 // 时、分、秒设为 0
        );

        // 获取当天 23:59:59
        this.endOfDay = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate(),
          23, 59, 59 // 时、分、秒设为 23:59:59
        );
        this.createTimeArr.push(this.startOfDay);
        this.createTimeArr.push(this.endOfDay);
        // 展示页面数据
        this.queryLists()
      },
      methods:{

        /*搜索按钮*/
        handleQuery(){
          this.queryLists();
        },
        queryLists(){
          if (this.createTimeArr != null && this.createTimeArr.length == 2) {
            this.selectParam.startTime = dayjs(this.createTimeArr[0]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.selectParam.endTime = dayjs(this.createTimeArr[1]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }else{
            this.selectParam.startTime = null;
            this.selectParam.endTime = null;
          }
          if(this.selectParam.backCanArriveFlag == null){
            this.selectParam.backCanArriveFlag = '未到达'
          }
          listIronSelect(this.selectParam).then(response=>{
            this.tableData = response.rows
            this.loading = false;
            this.mainTableConfig.pageConfig.total = response.total
          });
        },

        /*关闭编辑框触发 失去焦点 进行保存数据*/
        editClosedEvent(row, column) {
          this.ironCanWeightInputClose = row.row
        },
        /* 保存按钮 */
        clickSave(){
          console.log("this.ironCanWeightInputClose:",)
          if((JSON.stringify(this.ironCanWeightInputClose )== "{}") == false){
            ironCanWeightInputEdit(this.ironCanWeightInputClose).then(response=>{
              if(response.code == 200){
                this.$modal.msgSuccess("保存成功");
              }
              this.queryLists()
            });
          }else{
            this.$modal.msgError("请修改数据");
          }

        },
        /* 混罐 */
        clickMixedCan(){
          if(this.canTaskNumberList.length<=0){
            this.$modal.msgError("未选中罐");
            return;
          }
          mixedCanMethod(this.canTaskNumberList).then(response=>{
            this.$modal.msgSuccess("混罐成功");
            this.canTaskNumberList = []
            this.queryLists()
          });

        },
        /*多选框触发事件*/
        handleCheckboxChange({ records, rowIndex, row,checked }) {
         if(checked  == true){
           let obj ={canTaskNumber:row.canTaskNumber}
           this.canTaskNumberList.push(obj)
         }else{
           const $table = this.$refs.tableRef
           if ($table) {
             $table.clearCheckboxRow()
           }
           this.canTaskNumberList = []
         }
        },
        handleCellClickTime({ row, column }) {
          if (column.property === 'backCanStationArriveTime1') { // 判断是否点击的是时间列
            if(row.backCanStationArriveTime1 == null){
              row.backCanStationArriveTime1 = this.getCurrentTime(); // 获取当前时间并更新到行数据中
            }
          }
        },
        getCurrentTime() {
          return  dayjs(new Date()).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        },
        pageChange ({ pageSize, currentPage }) {
          this.mainTableConfig.pageConfig.pageNum = currentPage
          this.mainTableConfig.pageConfig.pageSize = pageSize
          this.selectParam.pageNum=this.mainTableConfig.pageConfig.pageNum
          this.selectParam.pageSize=this.mainTableConfig.pageConfig.pageSize
          this.queryLists()
        },
      },
    }
</script>

<style scoped>

</style>
