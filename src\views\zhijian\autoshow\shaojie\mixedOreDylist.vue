<template>
  <div class="mes_new_table">
    <el-form :model="queryParams" ref="queryForm" :inline="true" size="small" label-width="68px" style="margin-left: 20px;margin-top: 10px;">
      <el-form-item label="发布时间">
        <el-date-picker v-model="dateRange" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="烧结">
        <el-select v-model="queryParams.staveNo" placeholder="请选择">
          <el-option v-for="item in staveNoArr" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="queryList" icon="el-icon-search">查询</el-button>
      </el-form-item>
    </el-form>


    <vxe-table
      style="margin-left: 20px;margin-right: 20px;"
      border
      :data="tableData"
      :header-cell-style="headerCellStyle"
      @header-cell-click="headerCellClickEvent"
      :cell-config="{height: 35}"
      :loading="loading"
      :height="dltableHeight"
      @cell-click="cellClickEvent"
    >
      <vxe-column type="seq" width="70"></vxe-column>
      <vxe-column field="samplingLocationName" title="取样地点名称" width="auto" ></vxe-column>
      <vxe-column field="publishTime" title="发布时间" width="180"></vxe-column>
      <vxe-column field="tfe" title="TFe"></vxe-column>
      <vxe-column field="siO2" title="SiO2" ></vxe-column>
      <vxe-column field="al2O3" title="Al2O3" ></vxe-column>
      <vxe-column field="caO" title="CaO" ></vxe-column>
      <vxe-column field="mgO" title="MGO" ></vxe-column>
      <vxe-column field="mnO" title="MnO" ></vxe-column>
      <vxe-column field="tiO2" title="TiO2" ></vxe-column>
      <vxe-column field="p" title="P" ></vxe-column>
      <vxe-column field="s" title="S" ></vxe-column>
      <vxe-column field="cr2O3" title="Cr2O3" ></vxe-column>
      <vxe-column field="灼烧减量" title="灼烧减量" ></vxe-column>
      <vxe-column field="zn" title="Zn" ></vxe-column>
      <vxe-column field="h2O" title="H2O" ></vxe-column>
      <vxe-column field="asA" title="As" ></vxe-column>
    </vxe-table>
    <lineChart :LineChartData="LineChartData" class="linechartClass" />
  </div>
</template>

<script>
  import lineChart from '../../../components/chart/lineChart';
  import { lineEchartsComm } from '@/api/chart/lineChartTemplate.js';
  import {
    mixedOrelist
  } from "@/api/zhijian/dylist";
  import dayjs from "dayjs";
  export default {
    components: {
      lineChart
    },
    data(){
      return{
        loading:false,
        headerCellStyle:null,
        pageCharConfig:null,
        dltableHeight:400,
        tableData:[],
        LineChartData: {
          xData:  [], // X轴数据
          yData:  [] ,// Y轴数据（曲线形高度）
        },
        queryParams: {
          startTime:'',
          endTime:'',
          staveNo:'',
        },
        // 日期范围
        dateRange: [],
        staveNoArr:[{
          value: '1#烧结',
          label: '1#烧结'
        },{
          value: '2#烧结',
          label: '2#烧结'
        }],

      }
    },
    created() {
      this.dateRange.push(dayjs(new Date()).startOf("day"))
      this.dateRange.push(dayjs(new Date()));
      const obj = JSON.parse(JSON.stringify(this.queryParams))
      if (this.dateRange.length == 2) {
        obj.dtstart = dayjs(this.dateRange[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        obj.dtend = dayjs(this.dateRange[1]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        this.queryParams.startTime =  obj.dtstart
        this.queryParams.endTime =  obj.dtend

      }

      this.getListData();
      this.LineChartData.description = this.getFileName();  // 曲线元素名称
      let config = null;
      let lineConfig = lineEchartsComm().filter(x=>x.businessCode==this.getLineFileUrl());
      if(lineConfig != null || lineConfig.length > 0){
        config = lineConfig;
      }
      console.log("config:",JSON.stringify(config))
      if(config==null || config.length==0){
        //  提示
      }
      this.pageCharConfig=config[0];

      // 显示曲线得列填充颜色
      if(this.pageCharConfig.Y_line_axis.length>0){
        let tableFieldArr=[];
        for(let i=0;i<this.pageCharConfig.Y_line_axis.length;i++){
          tableFieldArr.push( this.pageCharConfig.Y_line_axis[i].tableField)
          this.headerCellStyle = ({ column }) => {
            if (tableFieldArr.includes(column.field ) ) {
              return {
                backgroundColor: '#ECF5FF',
                color: '#606266'
              }
            }
          }
        }
      }
    },
    methods: {
      /** 图形名称 */
      getFileName() {
        return this.$route.query.FileName;
      },
      /** 曲线图路径参数 */
      getLineFileUrl() {
        console.log("this.$route.query.lineFileUrl:",this.$route.query.lineFileUrl)
        return this.$route.query.lineFileUrl;
      },
      // 查询数据列表
      getListData(){
        if(this.queryParams.staveNo == '' || this.queryParams.staveNo == null){
          this.queryParams.staveNo = '1#烧结'
        }
        mixedOrelist(this.queryParams).then(response =>{
          console.log("response:",JSON.stringify(response))
          this.tableData = response.data
          if( this.tableData.length>0){
            var xData=[];
            var yData=[];
            let yitem = null;
            for (let i = 0; i < this.tableData.length ; i++) {
              const  item = this.tableData[i];
              this.LineChartData.seriesName1 = 'TFe'
              this.LineChartData.legendName = 'TFe'
              yData.push(item['tfe'])
              if(this.pageCharConfig.echarType == "曲线图"){
                xData.push(item[this.pageCharConfig.X_line_axis[0].tableField])
                this.LineChartData.yData = yData   // 曲线图 y1轴
                this.LineChartData.xData = xData   // 曲线图 x轴
              }
            }
          }
        })
      },

      queryList() {
        const obj = JSON.parse(JSON.stringify(this.queryParams))
        if (this.dateRange.length == 2) {
          obj.dtstart = dayjs(this.dateRange[0]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
          obj.dtend = dayjs(this.dateRange[1]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
          this.queryParams.startTime =  obj.dtstart
          this.queryParams.endTime =  obj.dtend
        }

        this.getListData();
      },
      /*表头单元格点击事件*/
      headerCellClickEvent ({ column }) {
        console.log(`表头单元格点击${column.field}`)
        let tableFild = '';
        tableFild=column.field;
        this.chartPlay(tableFild);
      },
      /* // 点击单列*/
      cellClickEvent ({ row, column }) {
        console.log("column:",column)
        let tableFild = '';
        tableFild=column.field;
        this.chartPlay(tableFild);
      },
      //曲线图展示
      chartPlay(tableFild){
        let yitem = null;
        if(this.pageCharConfig.echarType == "曲线图"){
          yitem = this.pageCharConfig.Y_line_axis.find(x=>x.tableField==tableFild);
          this.LineChartData.description = this.pageCharConfig.description; // 曲线元素名称
          this.LineChartData.seriesName1 = yitem.name
          this.LineChartData.legendName = yitem.name
        }

        if(yitem==null || yitem.length==0){
          return;
        }
        var xData=[];
        var yData=[];
        for (let i = 0; i < this.tableData.length ; i++) {
          const  item = this.tableData[i];
          yData.push(item[yitem.tableField])
          if(this.pageCharConfig.echarType == "曲线图"){
            xData.push(item[this.pageCharConfig.X_line_axis[0].tableField])
            this.LineChartData.yData = yData   // 曲线图 y1轴
            this.LineChartData.xData = xData   // 曲线图 x轴
          }
        }
      },


    },

  }
</script>

<style scoped>

</style>
