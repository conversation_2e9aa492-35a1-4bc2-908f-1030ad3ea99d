<template>
  <div class="app-container" :style="scaleform()" v-loading="loading" element-loading-text="正在拉取化学成分..."
    @dblclick="handleConDbClick" v-if="loadcomplateflag">
    <!-- 录入表单项目 -->
    <el-form ref="inputform" :model="planObj" label-width="auto"
      style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); padding: 10px;">
      <el-row :gutter="30">
        <el-col :span="4">
          <el-form-item label="高炉" size="medium">
            <el-select v-model="planObj.prod_code" placeholder="选择高炉" clearable class="feedplaninput"
              :disabled="isEmptyStr(propProdCode) ? false : true" @change="prodChange">
              <el-option v-for="item in optionProdCode" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="矿批" size="medium">
            <el-input v-model="planObj.oreSumWt" style="padding-left: 2px;padding-right: 2px;"
              class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="三次焦" size="medium">
            <el-input v-model="planObj.cokeNutSumWt" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="焦炭" size="medium">
            <el-input v-model="planObj.cokeSumWt" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="煤量" size="medium">
            <el-input v-model="planObj.coalSumWt" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="焦水" size="medium">
            <el-input v-model="planObj.cokeWater" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" align="middle" :gutter="30">
        <el-col :span="4">
          <el-form-item label="焦丁水" size="medium">
            <el-input v-model="planObj.cokeNutWater" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="风量" size="medium">
            <el-input v-model="planObj.airVol" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="氧量" size="medium">
            <el-input v-model="planObj.oxygen" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="进风面积" size="medium">
            <el-input v-model="planObj.airArea" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="料速" size="medium">
            <el-input v-model="planObj.mateSpeed" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <div style="display: flex;justify-content: flex-end;">
            <el-button-group size="medium">
              <!-- <el-button type="primary" icon="el-icon-cpu" circle :loading="loadingCalcBtn"
              @click="handlerCalc"></el-button>
            <el-button type="success" icon="el-icon-check" circle :loading="loadingSaveBtn"
              @click="handlerSave"></el-button> -->
              <el-button type="primary" :loading="loadingCalcBtn" @click="handlerCalc" size="mini">计算</el-button>
              <el-button type="success" :loading="loadingSaveBtn" @click="handlerSave" size="mini">保存</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="3">
          <el-form-item label="高炉">
            <el-select v-model="planObj.prod_code" placeholder="选择高炉" clearable>
              <el-option v-for="item in optionProdCode" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <el-row style="height: calc(100vh - 200px);">
      <el-col :span="17" style="height: 100%; overflow-y: auto; padding-right: 10px;">
        <vxe-toolbar ref="toolbarRefOre" custom>
          <template #buttons>
            <span class="boldspan1">矿</span>
          </template>
        </vxe-toolbar>
        <vxe-table id="ipesfeedingplanore" border show-overflow keep-source ref="tableRefOre"
          :custom-config="customConfig" :column-config="columnConfig" :cell-config="{ height: 24 }"
          :header-config="{ height: 24, rowHeight: 24 }" :footer-config="{ height: 24, rowHeight: 24 }" :style="{
            '--vxe-ui-table-border-color': '#000000',
            '--vxe-ui-table-border-width': '1px',
            '--vxe-ui-table-header-background-color': 'rgb(140, 197, 255)',
            '--vxe-ui-table-footer-background-color': 'rgb(140, 197, 255)'
          }" :data="planObj.listOre" :edit-rules="validRules" :edit-config="editConfig" :show-footer="true"
          :footer-method="footerMethodOre" @edit-disabled="editDisabledEvent" class="compact-table">
          <vxe-column field="materialNumber" title="矿料" :edit-render="{ name: 'VxeInput' }" width="220px"
            align="center">
            <template #edit="{ row }">
              <i class="el-icon-s-promotion flash" style="color: blue" @click="showelements(row)"></i>
              <el-select v-model="row.materialNumber" placeholder="请选择矿料" filterable :filter-method="filterOptionOre"
                @change="v => handerMateChange(v, row, oreoptions)" @visible-change="handlerVisibileChangerOre"
                style="width: 140px;height: 50%;" :popper-append-to-body="true" transfer="true">
                <el-option v-for="item in oreoptions" :key="item.value" :label="item.showtxt" :value="item.value">
                  <span style="float: left; margin-right: 10px;">{{ item.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.matenum
                    }}</span>
                  <span style="float: left; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.alias
                    }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.schemeinfo
                    }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.prodcode
                    }}</span>
                </el-option>
              </el-select>
              <i class="el-icon-delete" style="color: red" @click="delOre(row)"></i>
            </template>
            <template #default="{ row }">
              <div
                style="display: flex; align-items: center; justify-content: flex-start; width: 100%; text-align: left;">
                <i class="el-icon-s-promotion flash" style="color: blue; margin-left: 8px; flex-shrink: 0;"
                  @click.stop="showelements(row)"></i>
                <span style="flex: 1; text-align: center; margin-right: 8px; cursor: pointer;"
                  @click="editMaterialOre(row)">{{ row.materialName }}</span>
                <i class="el-icon-delete" style="color: red; cursor: pointer; flex-shrink: 0; margin-right: 8px;"
                  @click.stop="delOre(row)"></i>
              </div>
            </template>
            <template #header>
              <span>原料</span>
              <el-button style="width:30px; background-color: rgb(140, 197, 255);color:#000;text-align: center;"
                icon="el-icon-plus" size="mini" @click="addOre" type="text"></el-button>
            </template>
          </vxe-column>

          <vxe-colgroup title="物料配比" header-align="center">
            <vxe-column field="allRate" title="配比" :edit-render="{ name: 'VxeInput' }" width="80px"
              align="center"></vxe-column>
            <vxe-column field="h2oRate" title="水分" :edit-render="{ name: 'VxeInput' }" width="80px"
              align="center"></vxe-column>
            <vxe-column field="allWt" title="用量" :edit-render="{ name: 'VxeInput' }" width="80px"
              align="center"></vxe-column>
            <vxe-column field="allWetWt" title="干重量" :edit-render="{ name: 'VxeInput' }" width="80px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="Fe" header-align="center">
            <vxe-column field="tfeRate" title="Fe(%)" :edit-render="{ name: 'VxeInput' }" width="90px"
              align="center"></vxe-column>
            <vxe-column field="tfeWt" title="Fe(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="SiO2" header-align="center">
            <vxe-column field="sio2Rate" title="SiO2(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
            <vxe-column field="sio2Wt" title="SiO2(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="CaO" header-align="center">
            <vxe-column field="caoRate" title="CaO(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
            <vxe-column field="caoWt" title="CaO(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="S" header-align="center">
            <vxe-column field="sRate" title="S(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
            <vxe-column field="sWt" title="S(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="Al2O3" header-align="center">
            <vxe-column field="al2o3Rate" title="Al2O3(%)" :edit-render="{ name: 'VxeInput' }" width="75px"
              align="center"></vxe-column>
            <vxe-column field="al2o3Wt" title="Al2O3(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="MgO" header-align="center">
            <vxe-column field="mgoRate" title="MgO(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
            <vxe-column field="mgoWt" title="MgO(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>
        </vxe-table>

        <vxe-toolbar ref="toolbarRefCoke" custom>
          <template #buttons>
            <span class="boldspan1">焦类</span>
          </template>
        </vxe-toolbar>
        <vxe-table id="ipesfeedingplancoke" border show-overflow keep-source ref="tableRefCoke"
          :custom-config="customConfig" :column-config="columnConfig" :cell-config="{ height: 24 }"
          :header-config="{ height: 24, rowHeight: 24 }" :footer-config="{ height: 24, rowHeight: 24 }" :style="{
            '--vxe-ui-table-border-color': '#000000',
            '--vxe-ui-table-border-width': '1px',
            '--vxe-ui-table-header-background-color': 'rgb(225, 243, 216)',
            '--vxe-ui-table-footer-background-color': 'rgb(225, 243, 216)'
          }" :data="planObj.listCoke" :edit-rules="validRules" :edit-config="editConfig" :show-footer="true"
          :footer-method="footerMethodCoke" @edit-disabled="editDisabledEvent" class="compact-table">
          <vxe-column field="materialNumber" title="焦类" :edit-render="{ name: 'VxeInput' }" width="220px"
            align="center">
            <template #edit="{ row }">
              <i class="el-icon-s-promotion flash" style="color: blue" @click="showelements(row)"></i>
              <el-select v-model="row.materialNumber" placeholder="请选择焦类" filterable :filter-method="filterOptionCoke"
                @change="v => handerMateChange(v, row, cokeoptions)" @visible-change="handlerVisibileChangerCoke"
                style="width: 140px;height: 50%;" :popper-append-to-body="true" transfer="true">
                <el-option v-for="item in cokeoptions" :key="item.value" :label="item.showtxt" :value="item.value">
                  <span style="float: left; margin-right: 10px;">{{ item.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.matenum
                    }}</span>
                  <span style="float: left; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.alias
                    }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.schemeinfo
                    }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.prodcode
                    }}</span>
                </el-option>
              </el-select>
              <i class="el-icon-delete" style="color: red" @click="delCoke(row)"></i>
            </template>
            <template #default="{ row }">
              <div
                style="display: flex; align-items: center; justify-content: flex-start; width: 100%; text-align: left;">
                <i class="el-icon-s-promotion flash" style="color: blue; margin-left: 8px; flex-shrink: 0;"
                  @click.stop="showelements(row)"></i>
                <span style="flex: 1; text-align: center; margin-right: 8px; cursor: pointer;"
                  @click="editMaterialCoke(row)">{{ row.materialName }}</span>
                <i class="el-icon-delete" style="color: red; cursor: pointer; flex-shrink: 0; margin-right: 8px;"
                  @click.stop="delCoke(row)"></i>
              </div>
            </template>
            <template #header>
              <span>原料</span>
              <el-button style="width:30px; background-color: rgb(225, 243, 216);color:#000;text-align: center;"
                icon="el-icon-plus" size="mini" @click="addCoke" type="text"></el-button>
            </template>
          </vxe-column>

          <vxe-colgroup title="物料配比" header-align="center">
            <vxe-column field="allRate" title="配比" :edit-render="{ name: 'VxeInput' }" width="80px"
              align="center"></vxe-column>
            <vxe-column field="allWt" title="用量" :edit-render="{ name: 'VxeInput' }" width="80px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="SiO2" header-align="center">
            <vxe-column field="sio2Rate" title="SiO2(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
            <vxe-column field="sio2Wt" title="SiO2(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="CaO" header-align="center">
            <vxe-column field="caoRate" title="CaO(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
            <vxe-column field="caoWt" title="CaO(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="Al2O3" header-align="center">
            <vxe-column field="al2o3Rate" title="Al2O3(%)" :edit-render="{ name: 'VxeInput' }" width="75px"
              align="center"></vxe-column>
            <vxe-column field="al2o3Wt" title="Al2O3(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="MgO" header-align="center">
            <vxe-column field="mgoRate" title="MgO(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
            <vxe-column field="mgoWt" title="MgO(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-column field="fcadrate" title="固定碳(%)" :edit-render="{ name: 'VxeInput' }" width="80px"
            align="center"></vxe-column>
          <vxe-column field="adrate" title="A(%)" :edit-render="{ name: 'VxeInput' }" width="80px"
            align="center"></vxe-column>
          <vxe-column field="vdafrate" title="V(%)" :edit-render="{ name: 'VxeInput' }" width="80px"
            align="center"></vxe-column>
          <vxe-column field="sRate" title="S(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
            align="center"></vxe-column>
          <vxe-column field="h2oRate" title="H2O(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
            align="center"></vxe-column>
        </vxe-table>

        <vxe-toolbar ref="toolbarRefCokeNut" custom>
          <template #buttons>
            <span class="boldspan1">焦丁</span>
          </template>
        </vxe-toolbar>
        <vxe-table id="ipesfeedingplancokenut" border show-overflow keep-source ref="tableRefCokeNut"
          :custom-config="customConfig" :column-config="columnConfig" :cell-config="{ height: 24 }"
          :header-config="{ height: 24, rowHeight: 24 }" :footer-config="{ height: 24, rowHeight: 24 }" :style="{
            '--vxe-ui-table-border-color': '#000000',
            '--vxe-ui-table-border-width': '1px',
            '--vxe-ui-table-header-background-color': 'rgb(250, 236, 216)',
            '--vxe-ui-table-footer-background-color': 'rgb(250, 236, 216)'
          }" :data="planObj.listCokeNut" :edit-rules="validRules" :edit-config="editConfig" :show-footer="true"
          :footer-method="footerMethodCokeNut" @edit-disabled="editDisabledEvent" class="compact-table">
          <vxe-column field="materialNumber" title="焦丁" :edit-render="{ name: 'VxeInput' }" width="220px"
            align="center">
            <template #edit="{ row }">
              <i class="el-icon-s-promotion flash" style="color: blue" @click="showelements(row)"></i>
              <el-select v-model="row.materialNumber" placeholder="请选择焦丁" filterable
                :filter-method="filterOptionCokenut" @change="v => handerMateChange(v, row, cokenutoptions)"
                @visible-change="handlerVisibileChangerCokenut" style="width: 140px;height: 50%;"
                :popper-append-to-body="true" transfer="true">
                <el-option v-for="item in cokenutoptions" :key="item.value" :label="item.showtxt" :value="item.value">
                  <span style="float: left; margin-right: 10px;">{{ item.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.matenum
                    }}</span>
                  <span style="float: left; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.alias
                    }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.schemeinfo
                    }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.prodcode
                    }}</span>
                </el-option>
              </el-select>
              <i class="el-icon-delete" style="color: red" @click="delCokeNut(row)"></i>
            </template>
            <template #default="{ row }">
              <div
                style="display: flex; align-items: center; justify-content: flex-start; width: 100%; text-align: left;">
                <i class="el-icon-s-promotion flash" style="color: blue; margin-left: 8px; flex-shrink: 0;"
                  @click.stop="showelements(row)"></i>
                <span style="flex: 1; text-align: center; margin-right: 8px; cursor: pointer;"
                  @click="editMaterialCokeNut(row)">{{ row.materialName }}</span>
                <i class="el-icon-delete" style="color: red; cursor: pointer; flex-shrink: 0; margin-right: 8px;"
                  @click.stop="delCokeNut(row)"></i>
              </div>
            </template>
            <template #header>
              <span>原料</span>
              <el-button style="width:30px; background-color: rgb(250, 236, 216);color:#000;text-align: center;"
                icon="el-icon-plus" size="mini" @click="addCokeNut" type="text"></el-button>
            </template>
          </vxe-column>

          <vxe-colgroup title="物料配比" header-align="center">
            <vxe-column field="allRate" title="配比" :edit-render="{ name: 'VxeInput' }" width="80px"
              align="center"></vxe-column>
            <vxe-column field="allWt" title="用量" :edit-render="{ name: 'VxeInput' }" width="80px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="SiO2" header-align="center">
            <vxe-column field="sio2Rate" title="SiO2(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
            <vxe-column field="sio2Wt" title="SiO2(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="CaO" header-align="center">
            <vxe-column field="caoRate" title="CaO(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
            <vxe-column field="caoWt" title="CaO(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="Al2O3" header-align="center">
            <vxe-column field="al2o3Rate" title="Al2O3(%)" :edit-render="{ name: 'VxeInput' }" width="75px"
              align="center"></vxe-column>
            <vxe-column field="al2o3Wt" title="Al2O3(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="MgO" header-align="center">
            <vxe-column field="mgoRate" title="MgO(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
            <vxe-column field="mgoWt" title="MgO(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-column field="fcadrate" title="固定碳(%)" :edit-render="{ name: 'VxeInput' }" width="80px"
            align="center"></vxe-column>
          <vxe-column field="adrate" title="A(%)" :edit-render="{ name: 'VxeInput' }" width="80px"
            align="center"></vxe-column>
          <vxe-column field="vdafrate" title="V(%)" :edit-render="{ name: 'VxeInput' }" width="80px"
            align="center"></vxe-column>
          <vxe-column field="sRate" title="S(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
            align="center"></vxe-column>
          <vxe-column field="h2oRate" title="H2O(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
            align="center"></vxe-column>
        </vxe-table>

        <vxe-toolbar ref="toolbarRefCoal" custom>
          <template #buttons>
            <span class="boldspan1">煤</span>
          </template>
        </vxe-toolbar>
        <vxe-table id="ipesfeedingplancoal" border show-overflow keep-source ref="tableRefCoal"
          :custom-config="customConfig" :column-config="columnConfig" :cell-config="{ height: 24 }"
          :header-config="{ height: 24, rowHeight: 24 }" :footer-config="{ height: 24, rowHeight: 24 }" :style="{
            '--vxe-ui-table-border-color': '#000000',
            '--vxe-ui-table-border-width': '1px',
            '--vxe-ui-table-header-background-color': 'rgb(253, 226, 226)',
            '--vxe-ui-table-footer-background-color': 'rgb(253, 226, 226)'
          }" :data="planObj.listCoal" :edit-rules="validRules" :edit-config="editConfig" :show-footer="true"
          :footer-method="footerMethodCoal" @edit-disabled="editDisabledEvent" class="compact-table">
          <vxe-column field="materialNumber" title="煤" :edit-render="{ name: 'VxeInput' }" width="220px" align="center">
            <template #edit="{ row }">
              <i class="el-icon-s-promotion flash" style="color: blue" @click="showelements(row)"></i>
              <el-select v-model="row.materialNumber" placeholder="请选择煤" filterable :filter-method="filterOptionCoal"
                @change="v => handerMateChange(v, row, coaloptions)" @visible-change="handlerVisibileChangerCoal"
                style="width: 140px;height: 50%;" :popper-append-to-body="true" transfer="true">
                <el-option v-for="item in coaloptions" :key="item.value" :label="item.showtxt" :value="item.value">
                  <span style="float: left; margin-right: 10px;">{{ item.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.matenum
                    }}</span>
                  <span style="float: left; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.alias
                    }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.schemeinfo
                    }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.prodcode
                    }}</span>
                </el-option>
              </el-select>
              <i class="el-icon-delete" style="color: red" @click="delCoal(row)"></i>
            </template>
            <template #default="{ row }">
              <div
                style="display: flex; align-items: center; justify-content: flex-start; width: 100%; text-align: left;">
                <i class="el-icon-s-promotion flash" style="color: blue; margin-left: 8px; flex-shrink: 0;"
                  @click.stop="showelements(row)"></i>
                <span style="flex: 1; text-align: center; margin-right: 8px; cursor: pointer;"
                  @click="editMaterialCoal(row)">{{ row.materialName }}</span>
                <i class="el-icon-delete" style="color: red; cursor: pointer; flex-shrink: 0; margin-right: 8px;"
                  @click.stop="delCoal(row)"></i>
              </div>
            </template>
            <template #header>
              <span>原料</span>
              <el-button style="width:30px; background-color: rgb(253, 226, 226);color:#000;text-align: center;"
                icon="el-icon-plus" size="mini" @click="addCoal" type="text"></el-button>
            </template>
          </vxe-column>

          <vxe-colgroup title="物料配比" header-align="center">
            <vxe-column field="allRate" title="配比" :edit-render="{ name: 'VxeInput' }" width="80px"
              align="center"></vxe-column>
            <vxe-column field="allWt" title="用量" :edit-render="{ name: 'VxeInput' }" width="80px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="SiO2" header-align="center">
            <vxe-column field="sio2Rate" title="SiO2(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
            <vxe-column field="sio2Wt" title="SiO2(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="CaO" header-align="center">
            <vxe-column field="caoRate" title="CaO(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
            <vxe-column field="caoWt" title="CaO(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="Al2O3" header-align="center">
            <vxe-column field="al2o3Rate" title="Al2O3(%)" :edit-render="{ name: 'VxeInput' }" width="75px"
              align="center"></vxe-column>
            <vxe-column field="al2o3Wt" title="Al2O3(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-colgroup title="MgO" header-align="center">
            <vxe-column field="mgoRate" title="MgO(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
            <vxe-column field="mgoWt" title="MgO(t)" :edit-render="{ name: 'VxeInput' }" width="70px"
              align="center"></vxe-column>
          </vxe-colgroup>

          <vxe-column field="fcadrate" title="固定碳(%)" :edit-render="{ name: 'VxeInput' }" width="80px"
            align="center"></vxe-column>
          <vxe-column field="adrate" title="A(%)" :edit-render="{ name: 'VxeInput' }" width="80px"
            align="center"></vxe-column>
          <vxe-column field="vdafrate" title="V(%)" :edit-render="{ name: 'VxeInput' }" width="80px"
            align="center"></vxe-column>
          <vxe-column field="sRate" title="S(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
            align="center"></vxe-column>
          <vxe-column field="h2oRate" title="H2O(%)" :edit-render="{ name: 'VxeInput' }" width="70px"
            align="center"></vxe-column>
        </vxe-table>

        <!-- <vxe-toolbar ref="toolbarRefStatics" custom>
          <template #buttons>
            <span class="boldspan1">合计</span>
          </template>
        </vxe-toolbar>
        <vxe-table id="ipesfeedingplanstatics" border show-overflow keep-source ref="tableRefStatics"
          :custom-config="customConfig" :column-config="columnConfig" :cell-config="{ height: 24 }"
          :header-config="{ height: 24, rowHeight: 24 }" :style="{
            '--vxe-ui-table-border-color': '#000000',
            '--vxe-ui-table-border-width': '1px',
            '--vxe-ui-table-header-background-color': 'rgb(199, 209, 198)',
            '--vxe-ui-table-footer-background-color': 'rgb(199, 209, 198)'
          }" :data="planObj.listStatics" :edit-rules="validRules" :edit-config="editConfig"
          @edit-disabled="editDisabledEvent" class="compact-table">

          <vxe-column field="materialNumber" title="原料" width="220px" align="center"></vxe-column>
          <vxe-column field="allRate" title="配比%" width="80px" align="center"></vxe-column>
          <vxe-column field="allWt" title="配比kg" width="80px" align="center"></vxe-column>
          <vxe-column field="tfeWt" title="Fe重量" width="70px" align="center"></vxe-column>
          <vxe-column field="sio2Wt" title="SiO2重量" width="70px" align="center"></vxe-column>
          <vxe-column field="caoWt" title="CaO重量" width="70px" align="center"></vxe-column>
          <vxe-column field="mgoWt" title="MgO重量" width="70px" align="center"></vxe-column>
          <vxe-column field="al2o3Wt" title="Al2O3重量" width="70px" align="center"></vxe-column>
          <vxe-column field="sWt" title="S重量" width="70px" align="center"></vxe-column>
          <vxe-column field="pWt" title="P重量" width="70px" align="center" v-if="wtShowFlag"></vxe-column>
          <vxe-column field="znWt" title="Zn重量" width="70px" align="center" v-if="wtShowFlag"></vxe-column>
          <vxe-column field="tiWt" title="Ti重量" width="70px" align="center" v-if="wtShowFlag"></vxe-column>
          <vxe-column field="mnWt" title="Mn重量" width="70px" align="center" v-if="wtShowFlag"></vxe-column>
          <vxe-column field="kWt" title="K重量" width="70px" align="center" v-if="wtShowFlag"></vxe-column>
          <vxe-column field="naWt" title="Na重量" width="80px" align="center" v-if="wtShowFlag"></vxe-column>
          <vxe-column field="madwt" title="Mad重量" width="80px" align="center" v-if="wtShowFlag"></vxe-column>
          <vxe-column field="mtwt" title="Mt重量" width="80px" align="center" v-if="wtShowFlag"></vxe-column>
          <vxe-column field="adwt" title="Ad重量" width="80px" align="center" v-if="wtShowFlag"></vxe-column>
          <vxe-column field="fcadwt" title="FCad重量" width="80px" align="center" v-if="wtShowFlag"></vxe-column>
          <vxe-column field="vdafwt" title="Vdaf重量" width="80px" align="center" v-if="wtShowFlag"></vxe-column>
          <vxe-column field="qgrdwt" title="Qgr,d重量" width="80px" align="center" v-if="wtShowFlag"></vxe-column>
          <vxe-column field="density" title="堆比重" width="80px" align="center"></vxe-column>
          <vxe-column field="vol" title="体积" width="80px" align="center"></vxe-column>
        </vxe-table> -->


      </el-col>
      <el-col :span="7" style="text-align: center; height: 100%; overflow-y: auto;">
        <div class="indicators-container" style="margin-top: 15px; margin-left: 6px;">
          <!-- 高炉指标 -->
          <div class="blast-furnace-panel"
            style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); background-color: rgb(253, 246, 236);">
            <div class="panel-header">高炉指标</div>
            <div class="indicators-grid">

              <div class="indicator-cell">
                <el-tooltip content="R2=CaO/SiO2" placement="top">
                  <div class="cell-content">
                    <div class="cell-label">R2</div>
                    <div class="cell-value">{{ planObj.planmaintarget.r2 }}</div>
                  </div>
                </el-tooltip>
              </div>
              <div class="indicator-cell">
                <el-tooltip content="R4=(CaO+MgO)/(SiO2+Al2O3)" placement="top">
                  <div class="cell-content">
                    <div class="cell-label">R4</div>
                    <div class="cell-value">{{ planObj.planmaintarget.r4 }}</div>
                  </div>
                </el-tooltip>
              </div>
              <div class="indicator-cell">
                <el-tooltip content="R3=(CaO+MgO)/SiO2" placement="top">
                  <div class="cell-content">
                    <div class="cell-label">R3</div>
                    <div class="cell-value">{{ planObj.planmaintarget.r3 }}</div>
                  </div>
                </el-tooltip>
              </div>
              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">负荷</div>
                  <div class="cell-value">{{ planObj.planmaintarget.load }}</div>
                </div>
              </div>

              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">综合负荷</div>
                  <div class="cell-value">{{ planObj.planmaintarget.comLoad }}</div>
                </div>
              </div>
              <div class="indicator-cell">
                <el-tooltip content="渣量Kg/tFe" placement="top">
                  <div class="cell-content">
                    <div class="cell-label">渣量</div>
                    <div class="cell-value">{{ planObj.planmaintarget.slagWt }}</div>
                  </div>
                </el-tooltip>
              </div>
              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">风量</div>
                  <div class="cell-value">{{ planObj.airVol }}</div>
                </div>
              </div>
              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">氧量</div>
                  <div class="cell-value">{{ planObj.oxygen }}</div>
                </div>
              </div>


              <div class="indicator-cell">
                <el-tooltip content="综合焦比Kg/tFe" placement="top">
                  <div class="cell-content">
                    <div class="cell-label">综合焦比</div>
                    <div class="cell-value">{{ planObj.planmaintarget.comRatioOfCoke }}</div>
                  </div>
                </el-tooltip>
              </div>
              <div class="indicator-cell">
                <el-tooltip content="焦比Kg/tFe" placement="top">
                  <div class="cell-content">
                    <div class="cell-label">焦比</div>
                    <div class="cell-value">{{ planObj.planmaintarget.ratioOfCoke }}</div>
                  </div>
                </el-tooltip>
              </div>
              <div class="indicator-cell">
                <el-tooltip content="煤比Kg/tFe" placement="top">
                  <div class="cell-content">
                    <div class="cell-label">煤比</div>
                    <div class="cell-value">{{ planObj.planmaintarget.ratioOfCoal }}</div>
                  </div>
                </el-tooltip>
              </div>
              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">焦丁比</div>
                  <div class="cell-value">{{ planObj.planmaintarget.coke_nutRatio }}</div>
                </div>
              </div>

              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">料速</div>
                  <div class="cell-value">{{ planObj.mateSpeed }}</div>
                </div>
              </div>
              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">标准风速</div>
                  <div class="cell-value">{{ planObj.planmaintarget.stdWindSpeed }}</div>
                </div>
              </div>
              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">进风面积</div>
                  <div class="cell-value">{{ planObj.airArea }}</div>
                </div>
              </div>
              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">富氧率</div>
                  <div class="cell-value">{{ planObj.planmaintarget.oxRate }}</div>
                </div>
              </div>


              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">周期-批</div>
                  <div class="cell-value">{{ planObj.planmaintarget.periodBat }}</div>
                </div>
              </div>
              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">周期-时</div>
                  <div class="cell-value">{{ planObj.planmaintarget.periodHour }}</div>
                </div>
              </div>
              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">去焦丁</div>
                  <div class="cell-value">{{ planObj.planmaintarget.removeCoke_nut }}</div>
                </div>
              </div>
              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">矿耗</div>
                  <div class="cell-value">{{ planObj.planmaintarget.oreConsume }}</div>
                </div>
              </div>

              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">综合品位</div>
                  <div class="cell-value">{{ planObj.planmaintarget.comGrade }}</div>
                </div>
              </div>
              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">焦层厚度</div>
                  <div class="cell-value">{{ planObj.cokeThick }}</div>
                </div>
              </div>
              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">矿层厚度</div>
                  <div class="cell-value">{{ planObj.oreThick }}</div>
                </div>
              </div>
              <div class="indicator-cell">
                <div class="cell-content">
                  <div class="cell-label">料层厚度</div>
                  <div class="cell-value">{{ planObj.allThick }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="slag-container" style="margin-left: 6px; margin-top:10px">

          <div class="slag-panel"
            style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); background-color: rgb(179, 216, 255);">
            <div class="panel-header">炉渣成分</div>
            <div class="slag-grid">

              <div class="slag-header-cell">炉渣组成物</div>
              <div class="slag-header-cell">理论占比(%)</div>
              <div class="slag-header-cell">理论重量(kg)</div>
              <div class="slag-header-cell">实际重量(kg)</div>


              <div class="slag-cell">
                <div class="cell-content">
                  <div class="cell-label">SiO2</div>

                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.planslag.sio2ratio }}</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.planslag.sio2TheoryWt }}</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>

              <div class="slag-cell">
                <div class="cell-content">
                  <div class="cell-label">CaO</div>

                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.planslag.caoRatio }}</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.planslag.caoTheoryWt }}</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>


              <div class="slag-cell">
                <div class="cell-content">
                  <div class="cell-label">MgO</div>

                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.planslag.mgoRatio }}</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.planslag.mgoTheoryWt }}</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>



              <div class="slag-cell">
                <div class="cell-content">
                  <div class="cell-label">Al2O3</div>

                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.planslag.al2o3Ratio }}</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.planslag.al2o3TheoryWt }}</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>


              <div class="slag-cell">
                <div class="cell-content">
                  <div class="cell-label">矿耗</div>

                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.planslag.oreRatio }}</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>



              <div class="slag-cell">
                <div class="cell-content">
                  <div class="cell-label">CaS</div>

                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>

              <div class="slag-cell">
                <div class="cell-content">
                  <div class="cell-label">数量</div>

                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.planslag.staticsTheoryWt }}</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>


              <div class="slag-cell">
                <div class="cell-content">
                  <div class="cell-label">镁铝比</div>

                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.planslag.mg_alRatio }}</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>
              <div class="slag-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="iron-container" style="margin-left: 6px;margin-top:10px">
          <!-- 生铁成分表格 -->
          <!-- <div class="iron-panel"
            style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); background-color: rgb(225, 243, 216);">
            <div class="panel-header">生铁成分</div>
            <div class="iron-grid">

              <div class="iron-header-cell">生铁组成元素</div>
              <div class="iron-header-cell">理论成分占比(%)</div>
              <div class="iron-header-cell">理论成分重量(kg)</div>
              <div class="iron-header-cell">实际成分重量(kg)</div>


              <div class="iron-cell">
                <div class="cell-content">
                  <div class="cell-label">Fe</div>

                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">
                   
                 <el-input v-model="planObj.pigIronInfo.feRatio" size="mini"></el-input> 
                  </div>
                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>


              <div class="iron-cell">
                <div class="cell-content">
                  <div class="cell-label">Si</div>

                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">
                    <el-input v-model="planObj.pigIronInfo.siRatio" size="mini"></el-input>
                  </div>
                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.pigIronInfo.siTheoryWt }}</div>
                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>

              <div class="iron-cell">
                <div class="cell-content">
                  <div class="cell-label">Mn</div>

                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">
                    <el-input v-model="planObj.pigIronInfo.mnRatio" size="mini"></el-input>
                  </div>
                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.pigIronInfo.mnTheoryWt }}</div>
                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>


              <div class="iron-cell">
                <div class="cell-content">
                  <div class="cell-label">S</div>

                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">
                    <el-input v-model="planObj.pigIronInfo.sRatio" size="mini"></el-input>
                  </div>
                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>

              <div class="iron-cell">
                <div class="cell-content">
                  <div class="cell-label">P</div>

                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">
                    <el-input v-model="planObj.pigIronInfo.pRatio" size="mini"></el-input>
                  </div>
                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">-</div>
                </div>
              </div>

              <div class="iron-cell">
                <div class="cell-content">
                  <div class="cell-label">批料铁量</div>

                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value"></div>
                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.pigIronInfo.mateTheoryWt }}</div>
                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.pigIronInfo.mateActWt }}</div>
                </div>
              </div>

              <div class="iron-cell">
                <div class="cell-content">
                  <div class="cell-label"></div>
                  <div class="cell-value"></div>
                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">
                  <div class="cell-label"></div>
                  <div class="cell-value"></div>
                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">

                  <div class="cell-value">{{ planObj.pigIronInfo.mateStatics }}</div>
                </div>
              </div>
              <div class="iron-cell">
                <div class="cell-content">
                  <div class="cell-label"></div>
                  <div class="cell-value"></div>
                </div>
              </div>
            </div>
          </div> -->
        </div>
      </el-col>
    </el-row>
    <!-- 计算项 注释 -->
    <el-row :gutter="10" style="margin-top: 10px;" v-if="false">
      <!-- 生铁成分 -->
      <!-- style="background-color: bisque; border: 1px solid #000; "> -->
      <!-- <el-col :span="8" style="text-align: center; ">
        <el-form ref="stform" :model="planObj" label-width="auto"
          style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); padding: 10px; background-color: rgb(225, 243, 216); ">
          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col>
              <el-row><el-form-item>
                  <label>生铁成分</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>组成元素</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col>
                  <el-form-item>
                    <label>理论成分</label>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label>%</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>kg</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label>实际成分</label>
                </el-form-item></el-row>
              <el-row><el-form-item>
                  <label>kg</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>Fe</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                  
                    <el-input v-model="planObj.pigIronInfo.feRatio"></el-input>  
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label>-</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>Si</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <el-input v-model="planObj.pigIronInfo.siRatio"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.pigIronInfo.siTheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label>-</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>Mn</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <el-input v-model="planObj.pigIronInfo.mnRatio"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.pigIronInfo.mnTheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label>-</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>S</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <el-input v-model="planObj.pigIronInfo.sRatio"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label>-</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>P</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <el-input v-model="planObj.pigIronInfo.pRatio"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label>-</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>实际焦比</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label>{shijijiaobi}</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label> </label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label> </label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>实际综合焦</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label>{shijizonghejiao}</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label> </label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label> </label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>批料铁量</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label></label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.pigIronInfo.mateTheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                {{ planObj.pigIronInfo.mateActWt }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label></label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label></label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.pigIronInfo.mateStatics }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label></label>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-col> -->

      <!-- 炉渣成分 -->
      <!-- style="background-color:aquamarine; border: 1px solid #000; " -->
      <el-col :span="8" style="text-align: center; ">
        <el-form ref="lzform" :model="planObj" label-width="auto"
          style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); padding: 10px; background-color: rgb(179, 216, 255);">
          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col>
              <el-row><el-form-item>
                  <label>炉渣成分</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>组成物</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col>
                  <el-form-item>
                    <label>理论成分</label>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label>%</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>kg</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label>实际成分</label>
                </el-form-item></el-row>
              <el-row><el-form-item>
                  <label>kg</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>SiO2</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.planslag.sio2ratio }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.planslag.sio2TheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>CaO</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.planslag.caoRatio }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.planslag.caoTheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>MgO</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.planslag.mgoRatio }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.planslag.mgoTheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>Al2O3</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.planslag.al2o3Ratio }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.planslag.al2o3TheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>矿耗</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.planslag.oreRatio }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>CaS</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>数量</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.planslag.staticsTheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>镁铝比</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.planslag.mg_alRatio }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
      </el-col>

      <!-- 各种指标 -->
      <!-- style="background-color:bisque; border: 1px solid #000; " -->
      <el-col :span="8" style="text-align: center; ">
        <el-form ref="zbform" :model="planObj" label-width="auto" class="form_application"
          style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); padding: 10px; background-color: rgb(253, 246, 236);">
          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col>
              <el-row><el-form-item>
                  <label>各种指标</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-tooltip class="itemfeedinput" effect="dark" content="R2=CaO/SiO2" placement="left">
                <el-form-item label="R2" label-width="auto">
                  {{ planObj.planmaintarget.r2 }}
                </el-form-item>
              </el-tooltip>

            </el-col>
            <el-col :span="12">
              <el-form-item label="负荷">
                {{ planObj.planmaintarget.load }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-tooltip class="itemfeedinput" effect="dark" content="R4=(CaO+MgO)/(SiO2+Al2O3)" placement="left">
                <el-form-item label="R4" label-width="auto">
                  {{ planObj.planmaintarget.r4 }}
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="12">
              <el-form-item label="综合负荷">
                {{ planObj.planmaintarget.comLoad }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-tooltip class="itemfeedinput" effect="dark" content="渣量Kg/tFe" placement="left">
                <el-form-item label="渣量" label-width="auto">
                  {{ planObj.planmaintarget.slagWt }}
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="12">
              <el-form-item label="风量">
                {{ planObj.airVol }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-tooltip class="itemfeedinput" effect="dark" content="焦比Kg/tFe" placement="left">
                <el-form-item label="焦比" label-width="auto">
                  {{ planObj.planmaintarget.ratioOfCoke }}
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="12">
              <el-form-item label="氧量">
                {{ planObj.oxygen }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-tooltip class="itemfeedinput" effect="dark" content="煤比Kg/tFe" placement="left">
                <el-form-item label="煤比" label-width="auto">
                  {{ planObj.planmaintarget.ratioOfCoal }}
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="12">
              <el-form-item label="富氧率">
                {{ planObj.planmaintarget.oxRate }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-tooltip class="itemfeedinput" effect="dark" content="综合焦比Kg/tFe" placement="left">
                <el-form-item label="综合焦比" label-width="auto">
                  {{ planObj.planmaintarget.comRatioOfCoke }}
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="12">
              <el-form-item label="进风面积" label-width="auto">
                {{ planObj.airArea }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="料速" label-width="auto">
                {{ planObj.mateSpeed }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="标准风速">
                {{ planObj.planmaintarget.stdWindSpeed }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="冶炼周期（批）" label-width="auto">
                {{ planObj.planmaintarget.periodBat }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="焦丁比">
                {{ planObj.planmaintarget.coke_nutRatio }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="冶炼周期（小时）" label-width="auto">
                {{ planObj.planmaintarget.periodHour }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="综合品位">
                {{ planObj.planmaintarget.comGrade }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="去焦丁" label-width="auto">
                {{ planObj.planmaintarget.removeCoke_nut }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="矿耗">
                {{ planObj.planmaintarget.oreConsume }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="8">
              <el-form-item label="焦层厚度" label-width="auto">
                {{ planObj.cokeThick }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="矿层厚度">
                {{ planObj.oreThick }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="料层厚度">
                {{ planObj.allThick }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-col>
    </el-row>

    <!-- 化学成分选择 -->
    <el-dialog :title="dialogTitle" :visible.sync="openEle" v-if="openEle" width="80%" append-to-body>
      <eleminfo ref="eleminfo" :planEleCurrentObj="currOpRow" @reflashMate="handlerReflashMate"></eleminfo>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryFormManger,
  getMaterial,
  delMaterial,
  saveOrUpdate,
  getSaveEntity,
} from "@/api/md/material";
import { treeselectByGroup } from "@/api/md/materialCategory";
import { queryByUnitGropCode } from "@/api/md/materialUnit";
import Treeselect from "@riophae/vue-treeselect";
import { getToken } from "@/utils/auth";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { getemptydatamodel, getOrelist, getCokeList, getSolventList, getMateElemJS, getMateElemLT, getCoalList, calc, saveplan } from "@/api/feedingplan/feedingpage";
import { getCokeNutList, getOreAndSolvent, getdensity, bindserchbox, getMateElemALL, } from "@/api/feedingplan/feedingpage";
import json from "highlight.js/lib/languages/json";
import { mount } from "sortablejs";
import { pinyin, match } from 'pinyin-pro';
import { v4 as uuidv4 } from 'uuid';
import eleminfo from '@/views/ingredient/col/laboratoryReport'
import { vxetbcfgsave, vxetbcfgget } from "@/api/formtemplate/formdylist"
import { VxeUI } from 'vxe-table'

// 保存页面布局
const saveCustomSetting = (id, storeData) => {
  return new Promise(resolve => {
    vxetbcfgsave(id, storeData).then(resp => {
      if (resp.code == 200) {
        VxeUI.modal.message({
          status: 'success',
          content: '保存用户个性化数据成功'
        })
      }
    });
    resolve()
  })
}
// 查询页面布局
const findCustomSetting = (id) => {
  return new Promise(resolve => {
    try {
      vxetbcfgget(id).then(resp => {
        if (resp.code == "200") {
          resolve(resp.data);
          // VxeUI.modal.message({
          //   status: 'success',
          //   content: resp.msg
          // });
        }
      })
    } catch (e) {
      resolve({})
    }
  })
}

export default {
  components: { Treeselect, eleminfo },
  props: ['planid', 'srcplanid', 'propProdCode'],
  data() {
    const columnConfig = {
      resizable: true
    }
    const customConfig = {
      storage: true,
      allowResizable: true,
      restoreStore({ id }) {
        return findCustomSetting(id)
      },
      updateStore({ id, storeData }) {
        return saveCustomSetting(id, storeData)
      }
    }
    const validRules = {
      wetRate: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ],
      stockPrice: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ],
      elemTfe: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ],
      elemFeo: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ],
      elemSio2: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ],
      elemAl2o3: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ],
      elemCao: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ],
      elemMgo: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ],
      elemTio2: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ],
      elemP: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ],
      elemS: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ],
      elemMn: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ],
      elemZn: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ],
      elemH2o: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ]
    }
    const editConfig = {
      trigger: 'click',
      mode: 'row',
      showStatus: true,
      showIcon: false,
      beforeEditMethod({ row }) {
        if (row.edit) {
          return true
        }
        return false
      }
    }
    return {
      columnConfig,
      // 列个性化配置
      customConfig,
      validRules,
      editConfig,
      // 列表标题行显示标记
      showHeader: true,
      // 页面缩放比例
      currentRatio: 1,
      // 遮罩层
      loading: true,
      // 计算按钮loading
      loadingCalcBtn: false,
      // 保存方案按钮loading
      loadingSaveBtn: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openEle: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialCategoryCode: null,
        materialNumber: null,
        materialNumberDatacollect: null,
        materialName: null,
      },
      // 物料化学成分检化验结果
      materiaEle: [],
      // 弹出dialog显示的物料信息
      dialogTitle: '',
      // 弹出dialog计划化学成分
      planEle: [
        {
          ELETYPE: '当前',
          TFE_VALUE: null,
          SIO2_VALUE: null,
          CAO_VALUE: null,
          MGO_VALUE: null,
          AL2O3_VALUE: null,
          MATERIAL_CODE: '',
          DENSITY: null,
        },
        {
          ELETYPE: '平均值',
          TFE_VALUE: null,
          SIO2_VALUE: null,
          CAO_VALUE: null,
          MGO_VALUE: null,
          AL2O3_VALUE: null,
          MATERIAL_CODE: '',
          DENSITY: null,
        },
      ],
      // 表单参数
      form: {},
      // 矿批需要合计的列prop
      summaryCols: ["allRate", "allWt", "vol", "tfeWt", "sio2Wt", "caoWt", "mgoWt", "al2o3Wt", "sWt", "pWt", "znWt", "tiWt", "mnWt", "kWt", "naWt", "madwt", "mtwt", "adwt", "fcadwt", "vdafwt", "qgrdwt", "price"],
      weightAvgCols: ["tfeRate", "sio2Rate", "caoRate", "mgoRate", "al2o3Rate", "sRate", "pRate", "znRate", "tiRate", "mnRate", "kRate", "naRate", "madrate", "mtrate", "adrate", "fcadrate", "vdafrate", "qgrdrate"],
      // 计划数据对象
      planObj: {},
      // 空行对象
      materialobj: {},
      // 矿批下拉
      oreoptions: [],
      oreoptionsAll: [],
      oreoptionsSrc: [],
      // 焦下拉
      cokeoptions: [],
      cokeoptionsAll: [],
      cokeoptionsSrc: [],
      // 焦丁下拉
      cokenutoptions: [],
      cokenutoptionsAll: [],
      cokenutoptionsSrc: [],
      // 煤下拉
      coaloptions: [],
      coaloptionsAll: [],
      coaloptionsSrc: [],
      // 当前操作的物料编码
      currMateNum: '',
      // 当前操作的集合
      currOpList: [],
      // 当前操作的行
      currOpRow: null,
      // 计划ID
      // planid: '',
      // 重量显示标记_测试用
      wtShowFlag: false,
      // 加工中心下拉数据
      optionProdCode: [],
      // 渲染数据标记
      loadcomplateflag: false,
      // 矿料化学成分显示标记
      eleOreFlag: false,
    };
  },
  created() {
    this.loading = false;
    if (undefined != this.$route.query.planid) {
      this.planid = this.$route.query.planid;
    }
    bindserchbox(null, null).then(resp => {
      if (resp.code == 200) {
        // console.log(resp);
        this.optionProdCode = resp.data.prod;
        // this.optionStatus = resp.data.status;
        // 加载数据
        this.loadData();
      }
    }).catch(ex => { });
    // console.log("planid=", this.planid, "srcplanid=", this.srcplanid, "propProdCode=", this.propProdCode);
  },
  mounted() {
    const $tableRefOre = this.$refs.tableRefOre
    const $toolbarRefOre = this.$refs.toolbarRefOre
    if ($tableRefOre && $toolbarRefOre) {
      $tableRefOre.connect($toolbarRefOre)
    }
    // console.log("默认设置:",pinyin('汉语拼音'));
    // console.log("去掉声调:",pinyin('汉语拼音',{toneType: 'none'}));
    // console.log("hanpin连续:",match('汉语拼音', 'hanpin', { continuous: true }));
    // console.log("hupy连续:",match('汉语拼音', 'hypy', { continuous: true }));
    // console.log("py连续:",match('汉语拼音', 'py', { continuous: true }));
    // console.log("hp连续:",match('汉语拼音', 'hp', { continuous: true }));
    // console.log("uuid ",uuidv4());
  },
  methods: {
    // 页面内容加载
    loadData() {
      console.log("this.propProdCode", this.propProdCode);
      getemptydatamodel(this.planid, this.srcplanid, this.propProdCode).then((resp) => {
        // console.log(resp.data);
        // 等待绑定执行结束
        setTimeout(() => {
          if (resp.code === 200) {
            this.planObj = resp.data;
            this.materialobj = this.planObj.materialModel;
            this.planObj.planid = this.planid;
            if (!this.isEmptyStr(this.propProdCode)) {
              this.planObj.prod_code = this.propProdCode;
            }
            this.loadcomplateflag = true;
          }
        }, 0);
      });

      getOrelist().then(resp => {
        if (resp.code === 200) {
          this.oreoptions = resp.data;
          this.oreoptionsAll = resp.data;
          this.oreoptionsSrc = resp.data;
          if (!this.isEmptyStr(this.propProdCode)) {
            this.oreoptions = this.oreoptions.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode));
            this.oreoptionsAll = this.oreoptionsAll.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode))
          }
        }
      });
      getCokeList().then(resp => {
        if (resp.code === 200) {
          this.cokeoptions = resp.data;
          this.cokeoptionsAll = resp.data;
          this.cokeoptionsSrc = resp.data;
          if (!this.isEmptyStr(this.propProdCode)) {
            this.cokeoptions = this.cokeoptions.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode));
            this.cokeoptionsAll = this.cokeoptionsAll.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode))
          }
        }
      });
      getCoalList().then(resp => {
        if (resp.code === 200) {
          this.coaloptions = resp.data;
          this.coaloptionsAll = resp.data;
          this.coaloptionsSrc = resp.data;
          if (!this.isEmptyStr(this.propProdCode)) {
            this.coaloptions = this.coaloptions.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode));
            this.coaloptionsAll = this.coaloptionsAll.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode))
          }
        }
      });
      getCokeNutList().then(resp => {
        // console.log(resp);
        if (resp.code === 200) {
          this.cokenutoptions = resp.data;
          this.cokenutoptionsAll = resp.data;
          this.cokenutoptionsSrc = resp.data;
          if (!this.isEmptyStr(this.propProdCode)) {
            this.cokenutoptions = this.cokenutoptions.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode));
            this.cokenutoptionsAll = this.cokenutoptionsAll.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode))
          }
        }
      });
    },
    // 物料下拉事件
    handerMateChange(v, data, curroptions) {
      // 获取当前选项对象
      let curroptionObj = curroptions.filter(item => {
        return item.value === v
      })[0];
      // 获取堆比重
      getdensity(curroptionObj.matenum, curroptionObj.schemeno, this.planObj.prod_code).then(resp => {
        // console.log("getdensity响应 ", resp.data);
        if (resp.code == 200) {
          // 堆比重
          data.density = resp.data.mateinfo.densityValue;
          data.density = resp.data.mateinfo["densityValue" + this.planObj.prod_code]
          // 物料名称
          if (this.isEmptyStr(resp.data.mateinfo.aliasName)) {
            data.materialName = resp.data.mateinfo.materialName;
          } else {
            data.materialName = resp.data.mateinfo.aliasName;
          }
          // 方案信息
          // console.log("curroptions.schemeinfo",curroptionObj.schemeinfo);
          data.schemeInfo = curroptionObj.schemeinfo;
          // console.log("data.schemeinfo",data.schemeInfo);
          // 单价
          data.unitprice = resp.data.mateinfo.proposedPrice;
          // 设置展开
          // data.hasChildren = this.isEmptyStr(curroptionObj.schemeno);
          data.children = resp.data.schemeinfo;
          // console.log(data.materialName);
        }
      }).catch(err => {

      });
    },
    // 高炉切换
    prodChange(v) {
      if (!this.isEmptyStr(v)) {
        this.oreoptions = this.oreoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));
        this.oreoptionsAll = this.oreoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));

        this.cokeoptions = this.cokeoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));
        this.cokeoptionsAll = this.cokeoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));

        this.cokenutoptions = this.cokenutoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));
        this.cokenutoptionsAll = this.cokenutoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));

        this.coaloptions = this.coaloptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));
        this.coaloptionsAll = this.coaloptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));
      }
      else {
        this.oreoptions = this.oreoptionsSrc
        this.oreoptionsAll = this.oreoptionsSrc

        this.cokeoptions = this.cokeoptionsSrc
        this.cokeoptionsAll = this.cokeoptionsSrc

        this.cokenutoptions = this.cokenutoptionsSrc
        this.cokenutoptionsAll = this.cokenutoptionsSrc

        this.coaloptions = this.coaloptionsSrc
        this.coaloptionsAll = this.coaloptionsSrc
      }
    },
    // 调试用handler,大容器双击触发
    handleConDbClick() {
      //this.$message.success("双击");
      this.wtShowFlag = !this.wtShowFlag;
      // this.$message.success(this.wtShowFlag);
    },
    handlerCalc() {
      this.loadingCalcBtn = true;
      calc(this.planObj).then(resp => {
        if (resp.code === 200) {
          this.planObj = resp.data;
          this.$message.success("计算完成");
        }
        this.loadingCalcBtn = false;
      }).catch(err => {
        this.loadingCalcBtn = false;
        // this.$message.error(err);
      });
    },
    handlerSave() {

      console.log('计算前:', this.planObj);

      this.loadingSaveBtn = true;
      // 计算
      calc(this.planObj).then(resp => {
        if (resp.code === 200) {
          this.planObj = resp.data;
        }
      });
      console.log('计算后:', this.planObj);
      // 保存
      saveplan(this.planObj).then(resp => {
        if (resp.code === 200) {
          this.$message.success("保存成功");
        }
        this.loadingSaveBtn = false;
      }).catch(err => {
        this.loadingSaveBtn = false;
      });
    },
    handleSelectionChange1() {

    },
    addOre() {
      let modelMate = this.materialobj;
      modelMate.rowid = uuidv4();
      this.planObj.listOre.push(JSON.parse(JSON.stringify(modelMate)));
      // this.planObj.listOre.planitemid += 1;
    },
    // 编辑矿
    editMaterialOre(row) {
      if (row.edit) {
        row.edit = true;
        this.$nextTick(() => {

          this.$refs.tableRefOre.setActiveCell(row, 'materialNumber');
        });
      }
    },
    // 编辑焦类
    editMaterialCoke(row) {
      if (row.edit) {
        row.edit = true;
        this.$nextTick(() => {

          this.$refs.tableRefCoke.setActiveCell(row, 'materialNumber');
        });
      }
    },
    // 编辑焦丁
    editMaterialCokeNut(row) {
      if (row.edit) {
        row.edit = true;
        this.$nextTick(() => {

          this.$refs.tableRefCokeNut.setActiveCell(row, 'materialNumber');
        });
      }
    },
    // 编辑煤
    editMaterialCoal(row) {
      if (row.edit) {
        row.edit = true;
        this.$nextTick(() => {

          this.$refs.tableRefCoal.setActiveCell(row, 'materialNumber');
        });
      }
    },
    addCoke() {
      let modelMate = this.materialobj;
      modelMate.rowid = uuidv4();
      console.log("modelMate ", modelMate);
      this.planObj.listCoke.push(JSON.parse(JSON.stringify(modelMate)));
      // this.planObj.listCoke.planitemid += 1;
    },
    addCoal() {
      let modelMate = this.materialobj;
      modelMate.rowid = uuidv4();
      this.planObj.listCoal.push(JSON.parse(JSON.stringify(modelMate)));
      // this.planObj.listCoal.planitemid += 1;
    },
    addCokeNut() {
      let modelMate = this.materialobj;
      modelMate.rowid = uuidv4();
      this.planObj.listCokeNut.push(JSON.parse(JSON.stringify(modelMate)));
      // this.planObj.listCokeNut.planitemid += 1;
    },
    delOre(row) {
      if (!row.edit) {
        console.log('1111');
        return;
      }
      let itemid = row.rowid;
      this.planObj.listOre = this.planObj.listOre.filter(item => item.rowid !== itemid);
    },
    delCoke(row) {
      if (!row.edit) {
        console.log('1111');
        return;
      }
      let itemid = row.rowid;
      this.planObj.listCoke = this.planObj.listCoke.filter(item => item.rowid !== itemid);
    },
    delCoal(row) {
      if (!row.edit) {
        console.log('1111');
        return;
      }
      let itemid = row.rowid;
      this.planObj.listCoal = this.planObj.listCoal.filter(item => item.rowid !== itemid);
    },
    delCokeNut(row) {
      if (!row.edit) {
        console.log('1111');
        return;
      }
      let itemid = row.rowid;
      this.planObj.listCokeNut = this.planObj.listCokeNut.filter(item => item.rowid !== itemid);
    },
    // copyMateNum(mateNum) {
    //   clipboard.writeText(mateNum);
    //   this.$message.success("已复制物料编码:" + mateNum);
    // },
    /** 鼠标移入cell */
    handleCellEnter(row, column, cell, event) {
      // row.edit = true
    },
    /** 鼠标移出cell */
    handleCellLeave(row, column, cell, event) {
      // row.edit = false
      // row.edit = !row.edit;
    },
    handlerDBClick(row, column, cell, even) {
      row.edit = true;
    },
    cellClick(row) {
      row.edit = !row.edit;
    },
    editDisabledEvent({ row, column }) {
      VxeUI.modal.message({
        content: `物料[${row.materialname}]，不可编辑。`,
        status: 'warning'
      })
    },
    // 打开物料化学成分窗口
    showelementsNotOre(mtype, rowinfo) {
      // 根据物料编码获取近期检验结果
      this.loading = true;
      this.currOpRow = rowinfo;
      this.eleOreFlag = false;
      let tempMateNo = rowinfo.materialNumber.split(':')[0];
      // getMateElemALL(mtype, rowinfo.materialNumber).then((resp) => {
      getMateElemALL(mtype, tempMateNo).then((resp) => {
        try {
          if (resp.code == 200) {
            // console.log(JSON.stringify(resp));
            this.materiaEle = resp.data.samples;
            this.openEle = true;
            this.dialogTitle = '化验结果 ';
            this.dialogTitle += resp.data.MATERIAL_NAME;
            this.dialogTitle += ' 物料编码:' + rowinfo.materialNumber + ' ';
            // this.dialogTitle += ' 堆比重:' + resp.data.DENSITY_VALUE;
            let selectedDen = resp.data["DENSITY_VALUE_" + this.planObj.prod_code];
            selectedDen = this.isEmptyStr(selectedDen) ? "0.0" : selectedDen;
            this.dialogTitle += ' 堆比重:' + selectedDen;
            this.planEle[0].MATERIAL_CODE = rowinfo.materialNumber;
            this.planEle[1].MATERIAL_CODE = rowinfo.materialNumber;
            this.planEle[0].DENSITY = resp.data.DENSITY_VALUE;
            this.planEle[1].DENSITY = resp.data.DENSITY_VALUE;
            this.currMateNum = rowinfo.materialNumber;
          }
        } catch (error) {
          this.loading = false;
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },

    showelements(rowinfo) {
      // 根据物料编码获取近期检验结果
      this.loading = true;
      this.currOpRow = rowinfo;
      this.currOpRow.materialnumber = rowinfo.materialNumber
      this.currOpRow.materialname = rowinfo.materialName
      this.eleOreFlag = true;
      this.openEle = true;
      this.loading = false;
    },

    // 打开物料化学成分窗口
    // showelementsOre(rowinfo) {
    //   // 根据物料编码获取近期检验结果
    //   this.loading = true;
    //   this.currOpRow = rowinfo;
    //   this.eleOreFlag = true;

    //   let tempMateNo = rowinfo.materialNumber.split(':')[0];
    //   // getMateElemALL('ore', rowinfo.materialNumber).then((resp) => {
    //   getMateElemALL('ore', tempMateNo).then((resp) => {
    //     try {
    //       if (resp.code == 200) {
    //         // console.log(JSON.stringify(resp));
    //         this.materiaEle = resp.data.samples;
    //         this.openEle = true;
    //         this.dialogTitle = '化验结果 ';
    //         this.dialogTitle += resp.data.MATERIAL_NAME;
    //         this.dialogTitle += ' [' + rowinfo.materialNumber + '] ';
    //         // this.dialogTitle += '堆比重:' + resp.data.DENSITY_VALUE;
    //         let selectedDen = resp.data["DENSITY_VALUE_" + this.planObj.prod_code];
    //         selectedDen = this.isEmptyStr(selectedDen) ? "0.0" : selectedDen;
    //         this.dialogTitle += ' 堆比重:' + selectedDen;
    //         this.planEle[0].MATERIAL_CODE = rowinfo.materialNumber;
    //         this.planEle[1].MATERIAL_CODE = rowinfo.materialNumber;
    //         this.planEle[0].DENSITY = resp.data.DENSITY_VALUE;
    //         this.planEle[1].DENSITY = resp.data.DENSITY_VALUE;
    //         this.currMateNum = rowinfo.materialNumber;
    //       }
    //     } catch (error) {
    //       this.loading = false;
    //     }
    //     this.loading = false;
    //   }).catch(() => {
    //     this.loading = false;
    //   });
    // },
    handleSelectionChange(selection) {
      // TFE
      const valuesTFE = selection.map(item => Number(item['TFE_VALUE']));
      let avgTFE = this.getAvg(valuesTFE);
      this.planEle[1].TFE_VALUE = avgTFE;
      // SIO2
      const valuesSIO2 = selection.map(item => Number(item['SIO2_VALUE']));
      let avgSIO2 = this.getAvg(valuesSIO2);
      this.planEle[1].SIO2_VALUE = avgSIO2;
      // CAO
      const valuesCAO = selection.map(item => Number(item['CAO_VALUE']));
      let avgCAO = this.getAvg(valuesCAO);
      this.planEle[1].CAO_VALUE = avgCAO;
      // MGO
      const valuesMGO = selection.map(item => Number(item['MGO_VALUE']));
      let avgMGO = this.getAvg(valuesMGO);
      this.planEle[1].MGO_VALUE = avgMGO;
      // AL2O3
      const valuesAL2O3 = selection.map(item => Number(item['AL2O3_VALUE']));
      let avgAL2O3 = this.getAvg(valuesAL2O3);
      this.planEle[1].AL2O3_VALUE = avgAL2O3;
      // S
      const valuesS = selection.map(item => Number(item['S_VALUE']));
      let avgS = this.getAvg(valuesS);
      this.planEle[1].S_VALUE = avgS;
      // P
      const valuesP = selection.map(item => Number(item['P_VALUE']));
      let avgP = this.getAvg(valuesP);
      this.planEle[1].P_VALUE = avgP;
      // Zn
      const valuesZn = selection.map(item => Number(item['ZN_VALUE']));
      let avgZn = this.getAvg(valuesZn);
      this.planEle[1].ZN_VALUE = avgZn;
      // Ti
      const valuesTi = selection.map(item => Number(item['TI_VALUE']));
      let avgTi = this.getAvg(valuesTi);
      this.planEle[1].TI_VALUE = avgTi;
      // Mn
      const valuesMn = selection.map(item => Number(item['MN_VALUE']));
      let avgMn = this.getAvg(valuesMn);
      this.planEle[1].MN_VALUE = avgMn;
      // K
      const valuesK = selection.map(item => Number(item['K_VALUE']));
      let avgK = this.getAvg(valuesK);
      this.planEle[1].K_VALUE = avgK;
      // Na
      const valuesNa = selection.map(item => Number(item['NA_VALUE']));
      let avgNa = this.getAvg(valuesNa);
      this.planEle[1].NA_VALUE = avgNa;
      // Mad
      const valuesMad = selection.map(item => Number(item['MAD_VALUE']));
      let avgMad = this.getAvg(valuesMad);
      this.planEle[1].MAD_VALUE = avgMad;
      // Mt
      const valuesMt = selection.map(item => Number(item['MT_VALUE']));
      let avgMt = this.getAvg(valuesMt);
      this.planEle[1].MT_VALUE = avgMt;
      // Ad
      const valuesAd = selection.map(item => Number(item['AD_VALUE']));
      let avgAd = this.getAvg(valuesAd);
      this.planEle[1].AD_VALUE = avgAd;
      // Vdaf
      const valuesVdaf = selection.map(item => Number(item['VDAF_VALUE']));
      let avgVdaf = this.getAvg(valuesVdaf);
      this.planEle[1].VDAF_VALUE = avgVdaf;
      // Fcad
      const valuesFcad = selection.map(item => Number(item['FCAD_VALUE']));
      let avgFcad = this.getAvg(valuesFcad);
      this.planEle[1].FCAD_VALUE = avgFcad;
      // Fcad
      const valuesQgad = selection.map(item => Number(item['QGRD_VALUE']));
      let avgQgad = this.getAvg(valuesQgad);
      this.planEle[1].QGRD_VALUE = avgQgad;
    },
    getAvg(values) {
      let sum = 0;
      values.forEach((v, index) => {
        if (!isNaN(v)) {
          sum += v;
        }
      });
      let avg = sum / (values.length);
      if (avg >= 0.001) {
        return avg.toFixed(3);
      }
      else {
        return avg;
      }
    },
    handleCurrentChange(val) {
      this.currentRow = val;
      console.log(this.currentRow);
    },
    // selectedDialogELE() {
    //   this.currOpRow.tfeRate = this.planEle[1].TFE_VALUE;
    //   this.currOpRow.sio2Rate = this.planEle[1].SIO2_VALUE;
    //   this.currOpRow.caoRate = this.planEle[1].CAO_VALUE;
    //   this.currOpRow.mgoRate = this.planEle[1].MGO_VALUE;
    //   this.currOpRow.al2o3Rate = this.planEle[1].AL2O3_VALUE;
    //   this.currOpRow.pRate = this.planEle[1].P_VALUE;
    //   this.currOpRow.sRate = this.planEle[1].S_VALUE;
    //   this.currOpRow.znRate = this.planEle[1].ZN_VALUE;
    //   this.currOpRow.tiRate = this.planEle[1].TI_VALUE;
    //   this.currOpRow.mnRate = this.planEle[1].MN_VALUE;
    //   this.currOpRow.kRate = this.planEle[1].K_VALUE;
    //   this.currOpRow.naRate = this.planEle[1].NA_VALUE;
    //   this.currOpRow.madrate = this.planEle[1].MAD_VALUE;
    //   this.currOpRow.mtrate = this.planEle[1].MT_VALUE;
    //   this.currOpRow.adrate = this.planEle[1].AD_VALUE;
    //   this.currOpRow.vdafrate = this.planEle[1].VDAF_VALUE;
    //   this.currOpRow.fcadrate = this.planEle[1].FCAD_VALUE;
    //   this.currOpRow.qgrdrate = this.planEle[1].QGRD_VALUE;

    //   this.currOpRow.density = this.planEle[1].DENSITY;
    //   this.openEle = false;
    // },
    getSummariesOre(param) {
      return this.getSummaries(param, '矿批-小计');
    },
    getSummariesCoke(param) {
      return this.getSummaries(param, '焦-小计');
    },
    getSummariesCokeNut(param) {
      return this.getSummaries(param, '焦丁-小计');
    },
    getSummariesCoal(param) {
      return this.getSummaries(param, '煤-小计');
    },

    footerMethodOre({ columns, data }) {
      return [this.calculateFooter(columns, data, '矿批-小计')];
    },
    footerMethodCoke({ columns, data }) {
      return [this.calculateFooter(columns, data, '焦-小计')];
    },
    footerMethodCokeNut({ columns, data }) {
      return [this.calculateFooter(columns, data, '焦丁-小计')];
    },
    footerMethodCoal({ columns, data }) {
      return [this.calculateFooter(columns, data, '煤-小计')];
    },
    calculateFooter(columns, data, title) {
      return columns.map((column, index) => {
        if (index === 0) return title;

        const values = data.map(item => Number(item[column.field])).filter(val => !isNaN(val));
        if (values.length === 0) return '';

        if (this.weightAvgCols.includes(column.field)) {
          let sumValue = 0, sumWeight = 0;
          data.forEach(row => {
            if (!isNaN(row[column.field]) && !isNaN(row.allRate)) {
              sumValue += row[column.field] * row.allRate;
              sumWeight += row.allRate;
            }
          });
          return sumWeight > 0 ? (sumValue / sumWeight).toFixed(3) : '0.000';
        } else {
          return values.reduce((sum, val) => sum + val, 0).toFixed(3);
        }
      });
    },
    getSummaries(param, suntxt) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = suntxt + '';
          return;
        }
        // console.log(column.property);
        if (this.summaryCols.indexOf(column.property) >= 0) {
          const values = data.map(item => Number(item[column.property]));
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] = sums[index].toFixed(3);
        } else if (this.weightAvgCols.indexOf(column.property) >= 0) {
          const values = data.map(item => Number(item[column.property]));
          let sumValue = 0.0;
          let sumWeight = 0.0;
          data.forEach((v, i) => {
            if (!isNaN(v[column.property])) {
              sumValue += v[column.property] * v["allRate"];
              sumWeight += v["allRate"];
            }
          });
          if (sumWeight > 0) {
            sums[index] = sumValue / sumWeight;
            sums[index] = sums[index].toFixed(3);
          } else {
            sums[index] = 0;
          }
        }
      });
      return sums;
    },

    // 矿
    filterOptionOre(val) {
      if (this.isEmptyStr(val)) {
        this.oreoptions = this.oreoptionsAll;
      } else {
        this.oreoptions = this.oreoptionsAll.filter((item) => {
          return this.checkOption(item, val);
        });
      }
    },
    // 下拉框选项显示
    handlerVisibileChangerOre(e) {
      if (e) {
        this.oreoptions = this.oreoptionsAll;
      }
    },
    // 焦
    filterOptionCoke(val) {
      if (this.isEmptyStr(val)) {
        this.cokeoptions = this.cokeoptionsAll;
      } else {
        this.cokeoptions = this.cokeoptionsAll.filter((item) => {
          return this.checkOption(item, val);
        });
      }
    },
    // 下拉框选项显示
    handlerVisibileChangerCoke(e) {
      if (e) {
        this.cokeoptions = this.cokeoptionsAll;
      }
    },
    // 焦丁
    filterOptionCokenut(val) {
      if (this.isEmptyStr(val)) {
        this.cokenutoptions = this.cokenutoptionsAll;
      } else {
        this.cokenutoptions = this.cokenutoptionsAll.filter((item) => {
          return this.checkOption(item, val);
        });
      }
    },
    // 下拉框选项显示
    handlerVisibileChangerCokenut(e) {
      if (e) {
        this.cokenutoptions = this.cokenutoptionsAll;
      }
    },
    // 煤
    filterOptionCoal(val) {
      if (this.isEmptyStr(val)) {
        this.coaloptions = this.coaloptionsAll;
      } else {
        this.coaloptions = this.coaloptionsAll.filter((item) => {
          return this.checkOption(item, val);
        });
      }
    },
    // 下拉框选项显示
    handlerVisibileChangerCoal(e) {
      if (e) {
        this.coaloptions = this.coaloptionsAll;
      }
    },
    isEmptyStr(s) {
      if (s == undefined || s == null || s == '') {
        return true
      }
      return false
    },
    // 校验下拉框选项是否符合录入值
    checkOption(item, val) {
      // console.log(`${item.label} ${item.value} ${item.alias}`);        
      return !!~item.label.indexOf(val)
        ||
        !!~item.value.indexOf(val)
        ||
        !!~item.alias.indexOf(val)
        ||
        this.pinyinMatched(item.label, val)
        ||
        this.pinyinMatched(item.alias, val)
    },
    pinyinMatched(src, val) {
      const mRet = match(src, val, { continuous: true });
      if (mRet == null) {
        return false;
      }
      return true;
    },
    tableRowClassName({ row, rowIndex }) {
      // console.log('orebg');
      return 'orebg';
    },
    formatBoolean: function (row, column, cellValue) {
      var ret = ''
      // console.log(cellValue);
      if (cellValue) {
        ret = "是"
      } else {
        ret = "否"
      }
      return ret;
    },
    scaleform() {
      return {
        transform: `scale(${this.currentRatio},${this.currentRatio})`,
        padding: `5px`
      }
    },
    handlerReflashMate(planEleCurrentObj) {
      console.log("planEleCurrentObj",planEleCurrentObj);

      this.currOpRow.tfeRate = planEleCurrentObj.elemTfe;
      this.currOpRow.sio2Rate = planEleCurrentObj.elemSio2;
      this.currOpRow.caoRate = planEleCurrentObj.elemCao;
      this.currOpRow.mgoRate = planEleCurrentObj.elemMgo;
      this.currOpRow.al2o3Rate = planEleCurrentObj.elemAl2o3;
      this.currOpRow.pRate = planEleCurrentObj.elemP;
      this.currOpRow.sRate = planEleCurrentObj.elemS;
      this.currOpRow.znRate = planEleCurrentObj.elemZn;
      this.currOpRow.tiRate = planEleCurrentObj.elemTio2;
      this.currOpRow.mnRate = planEleCurrentObj.elemMn;
      this.currOpRow.adrate = planEleCurrentObj.elemA;
      this.currOpRow.vdafrate = planEleCurrentObj.elemV;
      this.currOpRow.fcadrate = planEleCurrentObj.elemC固;
      this.currOpRow.h2oRate = planEleCurrentObj.elemH2o
      this.currOpRow.density = planEleCurrentObj.DENSITY;
      this.openEle = false;
    },
  }
};
</script>

<style scoped>
.dataForm {
  margin: 0px;
  border-left: 1px solid #000000;
  border-bottom: 1px solid #000000;
}

.formItemDuty {
  border-top: 1px solid #000000;
  border-right: 1px solid #000000;
  margin: 0px;
}

.formItemDutyIn {
  border-left: 1px solid #000000;
  border-right: 1px solid #000000;
}


::deep .el-table .cell {
  white-space: nowrap;
  padding-left: 5px;
  padding-right: 5px;
  overflow: visible;
}

::deep .el-select-dropdown__wrap.el-scrollbar__wrap {
  margin-bottom: 0 !important;
}

/*闪烁动画*/
@keyframes twinkle {
  from {
    opacity: 1.0;
  }

  50% {
    opacity: 0.4;
  }

  to {
    opacity: 1.0;
  }
}

.flash {
  animation: twinkle 1s;
  animation-iteration-count: infinite;
}

.itemfeedinput {

  /* width: 100px; */

  .el-input__inner {
    padding-left: 2px;
    padding-right: 2px;
    height: 24px !important;
    border: none !important;
  }
}

.feedplaninput {

  /* width: 100px; */

  .el-input__inner {
    padding-left: 2px;
    padding-right: 2px;
    height: 24px !important;
    /* border: none!important; */
  }
}

.feedplaninput_long {

  /* width: 100px; */

  .el-input__inner {
    width: 80px !important;
    ;
    padding-left: 2px;
    padding-right: 2px;
    height: 24px !important;
    /* border: none!important; */
  }
}

.feedplaninput_mid {

  /* width: 100px; */

  .el-input__inner {
    width: 70px !important;
    ;
    padding-left: 2px;
    padding-right: 2px;
    height: 24px !important;
    /* border: none!important; */
  }
}

.el-form>>>.el-form-item__label {
  text-align: justify;
  text-align-last: justify;
}

.el-form-item {
  margin-bottom: 1px;
  height: 30px;
}

.el-form-item__label-wrap {
  margin-left: 0px !important;
}

.el-table>>>.el-select {
  width: 184px;
  height: 23px;

  .el-input__inner {
    height: 23px;
    border: none !important;
  }

  .el-input__prefix,
  .el-input__suffix {
    height: 23px;
  }


  .el-input__suffix {
    top: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
    flex-direction: row;
    align-content: flex-start;
  }


  .el-input__icon {
    line-height: 25px;
  }
}

.el-table /deep/ .el-table__footer-wrapper tbody td {
  height: 25px;
  padding-top: 2px;
  padding-bottom: 2px;
  font-weight: bolder;
  border-top: 1px solid #000000;
  border-bottom: 1px solid #000000;
  border-right: 1px solid #000000;

}

.el-table /deep/ .el-table__fixed-footer-wrapper tbody td {
  height: 25px;
  padding-top: 2px;
  padding-bottom: 2px;
  border-top: 1px solid #000000;
  border-bottom: 1px solid #000000;
  border-right: 1px solid #000000;
}

::v-deep .el-table .el-table__header th,
.el-table .el-table__header tr,
.el-table .el-table__header td {

  background: #c7d1c6 !important;
  padding: 0px;
  height: 25px;
  border-bottom: 1px solid #000000;
  border-right: 1px solid #000000;
}

.el-table {
  /* border: 1px solid #000000; */
  border-left: 1px solid #000000;
  border-top: 1px solid #000000;
}

.boldspan1 {
  font-weight: 700;
  color: #000000;
  font-size: 20px;
}


/* 保留dataForm样式用于其他表格 */

.dataForm .el-form-item__label {
  line-height: 1.1 !important;
  font-size: 11px !important;
  padding-bottom: 0 !important;
}

.dataForm .el-form-item__content {
  line-height: 1.1 !important;
  font-size: 11px !important;
}

/* .dataForm .el-col {
  padding: 1px !important;
} */

.dataForm .el-input--mini .el-input__inner {
  height: 20px !important;
  line-height: 20px !important;
  font-size: 11px !important;
}

/* 第一个列表文字居中对齐 */
.dataForm .el-form-item__label {
  text-align: center !important;
  justify-content: center !important;
}

.dataForm .el-form-item__content {
  text-align: center !important;
  justify-content: center !important;
}


.compact-table {
  font-size: 11px !important;
}

/* 重构后的高炉指标样式 */
.blast-furnace-panel {
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #000;
}

.panel-header {
  text-align: center;
  font-weight: bold;
  padding: 6px;
  border-bottom: 1px solid #000;
  font-size: 13px;
  background-color: rgb(253, 246, 236);
}

.indicators-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1px;
  background-color: #000;
}

.indicator-cell {
  background-color: rgb(253, 246, 236);
  border: none;
  min-height: 24px;
  max-height: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 4px;
  overflow: hidden;
}

.cell-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cell-label {
  flex: 1;
  font-size: small;
  color: #555;
  line-height: 1.2;
  white-space: nowrap;
  font-weight: 600;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cell-value {
  flex: 1;
  font-size: small;
  font-weight: 600;
  color: #222;
  line-height: 1.2;
  white-space: nowrap;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 炉渣成分表格样式 */
.slag-panel {
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #000;
}

.slag-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1px;
  background-color: #000;
}

.slag-header-cell {
  background-color: rgb(179, 216, 255);
  border: none;
  min-height: 24px;
  max-height: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 4px;
  font-size: 11px;
  font-weight: bold;
  text-align: center;
  overflow: hidden;
}

.slag-cell {
  background-color: rgb(179, 216, 255);
  border: none;
  min-height: 24px;
  max-height: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 4px;
  overflow: hidden;
}

/* 生铁成分表格样式 */
.iron-panel {
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #000;
}

.iron-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1px;
  background-color: #000;
}

.iron-header-cell {
  background-color: rgb(225, 243, 216);
  border: none;
  min-height: 24px;
  max-height: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 4px;
  font-size: 11px;
  font-weight: bold;
  text-align: center;
  overflow: hidden;
}

.iron-cell {
  background-color: rgb(225, 243, 216);
  border: none;
  min-height: 24px;
  max-height: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 4px;
  overflow: hidden;
}

/* 输入框样式调整 */
.iron-cell .el-input--mini,
.slag-cell .el-input--mini {
  height: 20px !important;
  line-height: 20px !important;
}

.iron-cell .el-input--mini .el-input__inner,
.slag-cell .el-input--mini .el-input__inner {
  height: 20px !important;
  line-height: 20px !important;
  font-size: 10px !important;
  padding: 0 3px !important;
  border: 1px solid #dcdfe6 !important;
  margin: 0 !important;
  min-height: 20px !important;
  max-height: 20px !important;
}

/* 确保输入框容器不会撑大行高 */
.iron-cell .cell-value,
.slag-cell .cell-value {
  min-height: 20px !important;
  max-height: 20px !important;
}


.compact-table :deep(.vxe-cell),
.compact-table ::v-deep .vxe-cell,
.compact-table /deep/ .vxe-cell {
  padding: 1px 2px !important;
  line-height: 18px !important;
  text-align: center !important;
  font-size: 11px !important;
}

.compact-table :deep(.vxe-header--column),
.compact-table ::v-deep .vxe-header--column,
.compact-table /deep/ .vxe-header--column {
  text-align: center !important;
  padding: 1px 2px !important;
}

.compact-table :deep(.vxe-body--column),
.compact-table ::v-deep .vxe-body--column,
.compact-table /deep/ .vxe-body--column {
  text-align: center !important;
  padding: 1px 2px !important;
}

.compact-table :deep(.vxe-footer--column),
.compact-table ::v-deep .vxe-footer--column,
.compact-table /deep/ .vxe-footer--column {
  text-align: center !important;
  padding: 1px 2px !important;
}


.vxe-toolbar {
  margin-top: 6px !important;
  margin-bottom: 2px !important;
  padding: 2px 0 !important;
}

.vxe-table {
  margin-bottom: 4px !important;
}


.compact-table :deep(.vxe-table--render-default .vxe-body--row),
.compact-table ::v-deep .vxe-table--render-default .vxe-body--row,
.compact-table /deep/ .vxe-table--render-default .vxe-body--row {
  height: 24px !important;
}

.compact-table :deep(.vxe-table--render-default .vxe-header--row),
.compact-table ::v-deep .vxe-table--render-default .vxe-header--row,
.compact-table /deep/ .vxe-table--render-default .vxe-header--row {
  height: 24px !important;
}

.compact-table :deep(.vxe-table--render-default .vxe-footer--row),
.compact-table ::v-deep .vxe-table--render-default .vxe-footer--row,
.compact-table /deep/ .vxe-table--render-default .vxe-footer--row {
  height: 24px !important;
}

#ipesfeedingplanore,
#ipesfeedingplancoke,
#ipesfeedingplancokenut,
#ipesfeedingplancoal,
#ipesfeedingplanstatics {
  font-size: 11px !important;
}

#ipesfeedingplanore .vxe-cell,
#ipesfeedingplancoke .vxe-cell,
#ipesfeedingplancokenut .vxe-cell,
#ipesfeedingplancoal .vxe-cell,
#ipesfeedingplanstatics .vxe-cell {
  padding: 1px 2px !important;
  line-height: 18px !important;
  font-size: 11px !important;
}


.boldspan1 {
  font-weight: 700;
  color: #000000;
  font-size: 16px !important;
}


.compact-table /deep/ .vxe-input {
  text-align: center !important;
  font-size: 12px !important;
  height: 24px !important;
  line-height: 24px !important;
  border: 0;
  background-color: transparent;
}

.compact-table /deep/ .vxe-input .vxe-input--inner {
  text-align: center !important;
  font-size: 12px !important;
  height: 24px !important;
  line-height: 24px !important;
  padding: 0 3px !important;
}




.compact-table /deep/ .el-select .el-input__inner {
  width: 80% !important;
  text-align: center !important;
  font-size: 12px !important;
  /* height: 24px !important; */
  line-height: 24px !important;
  padding: 0 3px !important;
  border: none !important;
  box-shadow: none !important;
}

.compact-table /deep/ .el-select .el-input__inner:focus {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.compact-table /deep/ .el-select .el-input__inner:hover {
  border: none !important;
  box-shadow: none !important;
}

.compact-table /deep/ .el-select {
  width: 80% !important;
  border: none !important;
  box-shadow: none !important;
}


.compact-table /deep/ .el-select:focus,
.compact-table /deep/ .el-select:hover,
.compact-table /deep/ .el-select:active {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}


.compact-table /deep/ .vxe-body--column[data-colid="materialnumber"] .vxe-cell,
.compact-table /deep/ td[data-colid="materialnumber"] .vxe-cell,
.compact-table /deep/ .vxe-table .vxe-body--column[data-colid="materialnumber"] .vxe-cell {
  text-align: left !important;
  padding-left: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  flex-direction: row !important;
}

.compact-table /deep/ .vxe-header--column[data-colid="materialnumber"] .vxe-cell {
  text-align: center !important;
}


.compact-table /deep/ .vxe-body--column[data-colid="materialnumber"] .vxe-cell .el-icon-s-promotion,
.compact-table /deep/ td[data-colid="materialnumber"] .vxe-cell .el-icon-s-promotion {
  margin-right: 8px !important;
  flex-shrink: 0 !important;
  order: 1 !important;
}

.compact-table /deep/ .vxe-body--column[data-colid="materialnumber"] .vxe-cell span,
.compact-table /deep/ td[data-colid="materialnumber"] .vxe-cell span {
  flex: 1 !important;
  margin-left: 0 !important;
  margin-right: 8px !important;
  text-align: left !important;
  order: 2 !important;
}

.compact-table /deep/ .vxe-body--column[data-colid="materialnumber"] .vxe-cell .el-icon-delete,
.compact-table /deep/ td[data-colid="materialnumber"] .vxe-cell .el-icon-delete {
  margin-left: auto !important;
  flex-shrink: 0 !important;
  order: 3 !important;
}


.compact-table /deep/ .vxe-body--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-header--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-footer--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}


.compact-table /deep/ .vxe-body--row:first-child {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-body--row:last-child {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}


.compact-table /deep/ .vxe-table--body .vxe-body--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}


.compact-table /deep/ .vxe-body--column {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-header--column {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-footer--column {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table {
  line-height: 1.2 !important;
}

.compact-table /deep/ .vxe-table--body-wrapper {
  font-size: 12px !important;
}

.compact-table /deep/ .vxe-table--header-wrapper {
  font-size: 12px !important;
}

.compact-table /deep/ .vxe-table--footer-wrapper {
  font-size: 12px !important;
}

.compact-table /deep/ .vxe-table--render-default .vxe-body--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--render-default .vxe-header--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--render-default .vxe-footer--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}


.compact-table /deep/ .vxe-body--row.row--edit {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}


.compact-table /deep/ .vxe-body--row.row--selected {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}


.compact-table /deep/ .vxe-body--row.row--hover {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}


.compact-table /deep/ tr {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}


.compact-table /deep/ td {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  padding: 2px 3px !important;
}


.compact-table /deep/ th {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  padding: 2px 3px !important;
}


.compact-table /deep/ .vxe-table--footer-wrapper {
  height: auto !important;
}

.compact-table /deep/ .vxe-table--footer-wrapper .vxe-footer--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--footer-wrapper .vxe-footer--column {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--footer-wrapper .vxe-footer--column .vxe-cell {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  line-height: 26px !important;
  padding: 2px 3px !important;
}


.compact-table /deep/ .vxe-table--footer-wrapper tr {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--footer-wrapper td {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  padding: 2px 3px !important;
}


.compact-table /deep/ .vxe-table--render-default .vxe-table--footer-wrapper .vxe-footer--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}


.compact-table /deep/ .vxe-footer--row.row--hover {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}


.compact-table /deep/ .vxe-table--header-wrapper {
  height: auto !important;
}

.compact-table /deep/ .vxe-table--header-wrapper .vxe-header--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--header-wrapper .vxe-header--column {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--header-wrapper .vxe-header--column .vxe-cell {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  line-height: 26px !important;
  padding: 2px 3px !important;
}


.compact-table /deep/ .vxe-table--header-wrapper tr {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--header-wrapper th {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  padding: 2px 3px !important;
}


.compact-table /deep/ .vxe-table--render-default .vxe-table--header-wrapper .vxe-header--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}


.compact-table /deep/ .vxe-header--row.row--hover {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}


.compact-table /deep/ .vxe-table--header-wrapper .el-button {
  height: 24px !important;
  min-height: 24px !important;
  max-height: 24px !important;
  padding: 2px 4px !important;
  line-height: 20px !important;
}


.compact-table /deep/ .vxe-table--body-inner-wrapper {
  min-height: auto !important;

}





/* .el-table {
  border-bottom: 1px solid #000000 !important;
} */
</style>