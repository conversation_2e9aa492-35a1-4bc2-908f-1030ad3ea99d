<template>
  <div class="stacker-reclaimer-component" :class="{ 'container-mode': containerMode }">
    <div class="stacker-reclaimer"
         :class="deviceClasses"
         :style="deviceStyle"
         @click="handleClick"
         @mouseenter="handleMouseEnter"
         @mouseleave="handleMouseLeave">

      <div class="device-body">
        <div class="rotating-platform" :style="platformRotationStyle">

          <div class="machine-house">
            <div class="house-main"></div>
            <div class="house-details">
              <div class="device-number">{{ deviceNumber }}</div>
              <div class="control-panels"></div>
            </div>

            <div class="support-legs">
              <div class="leg leg-1"></div>
              <div class="leg leg-2"></div>
              <div class="leg leg-3"></div>
              <div class="leg leg-4"></div>
            </div>
          </div>

          <div class="boom-assembly" :style="boomRotationStyle">
            <div class="boom-main">
              <div class="boom-beam boom-main-arm"></div>
              <div class="boom-structure boom-main-structure"></div>
              <div class="boom-beam boom-counter-arm"></div>
            </div>

            <div class="bucket-wheel-assembly">
              <div class="bucket-wheel" :class="{
                'spinning': isWorking,
                'spinning-clockwise': isWorking && bucketWheelDirection === 'clockwise',
                'spinning-counterclockwise': isWorking && bucketWheelDirection === 'counterclockwise'
              }">
                <div class="wheel-center"></div>
                <div class="wheel-blades">
                  <div class="blade" v-for="i in 8" :key="i"
                       :style="{ transform: `rotate(${i * 45}deg)` }"></div>
                </div>
              </div>
            </div>

            <div class="counterweight-area">
              <div class="counterweight-block"></div>
            </div>
          </div>

        </div>
      </div>

      <div class="position-label">
        {{ Math.round(data.position.scale) }}
      </div>
    </div>

    <div v-if="showDetails" class="details-panel" :style="detailsPanelStyle">
      <div class="panel-header">
        <h4>{{ data.name }}</h4>
        <span class="status-text">{{ statusText }}</span>
      </div>
      <div class="panel-content">
        <div class="info-row">
          <span>位置:</span>
          <span>{{ Math.round(data.position.scale) }}m</span>
        </div>
        <div class="info-row">
          <span>悬臂角度:</span>
          <span>{{ data.equipment.boomAngle }}°</span>
        </div>
        <div class="info-row" v-if="data.operation.mode !== 'idle'">
          <span>作业模式:</span>
          <span>{{ operationText }}</span>
        </div>
        <div class="info-row" v-if="data.operation.targetMaterial">
          <span>目标物料:</span>
          <span>{{ data.operation.targetMaterial }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StackerReclaimerNew',
  props: {
    data: {
      type: Object,
      required: true,
      default: () => ({
        id: 'sr-001',
        name: '堆取料机#1',
        position: {
          scale: 350
        },
        equipment: {
          boomAngle: 45,
          status: 'working',
          bucketWheelSpeed: 120
        },
        operation: {
          mode: 'reclaiming',
          targetMaterial: '焦粉'
        }
      })
    },

    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    },

    interactive: {
      type: Boolean,
      default: true
    },

    positionStyle: {
      type: Object,
      default: () => ({})
    },

    containerMode: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      showDetails: false,
      detailsPanelPosition: { x: 0, y: 0 }
    };
  },
  
  computed: {
    deviceNumber() {
      return this.data.name.match(/\d+/)?.[0] || '1';
    },

    deviceClasses() {
      return [
        `size-${this.size}`,
        `status-${this.data.equipment.status}`,
        {
          'interactive': this.interactive,
          'working': this.isWorking,
          'moving': this.data.equipment.status === 'moving'
        }
      ];
    },

    deviceStyle() {
      return {
        ...this.positionStyle
      };
    },

    platformRotationStyle() {
      return {
        transform: 'rotate(0deg)',
        transformOrigin: 'center center'
      };
    },

    boomRotationStyle() {
      return {
        transform: `rotate(${this.data.equipment.boomAngle}deg)`,
        transformOrigin: '0% 50%',
        transition: this.data.equipment.status === 'moving' ?
          'transform 1.5s ease-in-out' : 'transform 0.5s ease'
      };
    },

    isWorking() {
      return this.data.equipment.status === 'working' &&
             this.data.equipment.bucketWheelSpeed > 0;
    },

    bucketWheelDirection() {
      return this.data.equipment.bucketWheelDirection || 'clockwise';
    },

    statusClass() {
      return `status-${this.data.equipment.status}`;
    },

    statusText() {
      const statusMap = {
        'idle': '待机',
        'working': '作业中',
        'moving': '移动中',
        'maintenance': '维护'
      };
      return statusMap[this.data.equipment.status] || '未知';
    },

    operationText() {
      const modeMap = {
        'stacking': '堆料',
        'reclaiming': '取料',
        'idle': '待机'
      };
      return modeMap[this.data.operation.mode] || '';
    },

    detailsPanelStyle() {
      return {
        left: `${this.detailsPanelPosition.x}px`,
        top: `${this.detailsPanelPosition.y}px`
      };
    }
  },
  
  methods: {
    handleClick() {
      if (!this.interactive) return;
      this.$emit('click', this.data);
    },

    handleMouseEnter(event) {
      if (!this.interactive) return;
      this.showDetails = true;
      this.updateDetailsPanelPosition(event);
      this.$emit('hover', this.data);
    },

    handleMouseLeave() {
      this.showDetails = false;
      this.$emit('leave', this.data);
    },

    updateDetailsPanelPosition(event) {
      // 获取组件容器和大机元素的尺寸
      const stackerRect = event.currentTarget.getBoundingClientRect();
      const containerRect = this.$el.getBoundingClientRect();

      // 计算相对于组件容器的位置
      const relativeX = stackerRect.left - containerRect.left;
      const relativeY = stackerRect.top - containerRect.top;

      // 默认显示在大机右侧
      let x = relativeX + stackerRect.width + 10;
      let y = relativeY;

      // 简单的边界检测：如果右侧空间不够，显示在左侧
      const panelWidth = 250;
      const containerWidth = containerRect.width;

      if (x + panelWidth > containerWidth) {
        // 显示在左侧
        x = relativeX - panelWidth - 10;
        // 如果左侧也不够，显示在上方
        if (x < 0) {
          x = relativeX;
          y = relativeY - 160; // 预估面板高度 + 间距
        }
      }

      this.detailsPanelPosition = { x, y };
    }
  }
};
</script>

<style lang="scss" scoped>
.stacker-reclaimer-component {
  position: relative;
  display: inline-block;
  vertical-align: top;
  overflow: visible;
  min-width: 100px;
  min-height: 60px;
}

.stacker-reclaimer-component.container-mode {
  padding: 15px 10px 20px 10px;
  margin: 0;
}

.stacker-reclaimer {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;

  &.interactive:hover {
    transform: scale(1.05);
  }

  &.working {
    animation: deviceWorking 3s ease-in-out infinite;
  }

  &.moving {
    animation: deviceMoving 2s ease-in-out infinite;
  }
}

@keyframes deviceWorking {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

@keyframes deviceMoving {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-1px); }
}

/* 设备主体 - 俯视图 */
.device-body {
  position: relative;
  width: 120px;
  height: 100px;
}

/* 轨道 - 俯视图显示两条平行轨道 */
.track-rails {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 100%;
  z-index: 1;
}

.rail {
  position: absolute;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #5a5a5a, #3a3a3a, #2a2a2a);
  border-radius: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.rail-left {
  left: 20%;
}

.rail-right {
  right: 20%;
}

/* 旋转平台 - 俯视图 */
.rotating-platform {
  position: absolute;
  //top: 50%;
  left: 0;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 80px;
  transform-origin: center center;
  z-index: 10;
}

/* 机房/控制室 - 水平长方形结构 */
.machine-house {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 35px;
}

.house-main {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #3498db, #2980b9, #1f4e79);
  border: 2px solid #1a4480;
  border-radius: 4px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
  position: relative;
}

.house-main::before {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  right: 3px;
  bottom: 3px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.house-details {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.device-number {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.control-panels {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 1px;
}

/* 支腿/稳定器 */
.support-legs {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.leg {
  position: absolute;
  width: 6px;
  height: 6px;
  background: linear-gradient(45deg, #34495e, #2c3e50);
  border: 1px solid #1a252f;
  border-radius: 1px;
}

.leg-1 { top: -3px; left: -3px; }
.leg-2 { top: -3px; right: -3px; }
.leg-3 { bottom: -3px; left: -3px; }
.leg-4 { bottom: -3px; right: -3px; }

/* 悬臂结构 - 俯视图 */
.boom-assembly {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translate(-33.33%, -50%);
  width: 150px;
  height: 30px;
  transform-origin: 33.33% 50%;
}

.boom-main {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 主悬臂（向斗轮方向） */
.boom-main-arm {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  width: 100px;
  height: 8px;
  background: linear-gradient(to right, #7f8c8d, #95a5a6, #7f8c8d);
  border: 2px solid #5d6d7e;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.boom-main-arm::before {
  content: '';
  position: absolute;
  top: 1px;
  left: 2px;
  right: 2px;
  height: 2px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.3), transparent, rgba(255, 255, 255, 0.3));
  border-radius: 2px;
}

.boom-main-structure {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  width: 95px;
  height: 4px;
  background: linear-gradient(to right, #95a5a6, #7f8c8d);
  border-radius: 2px;
}

.boom-main-structure::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 10px;
  width: 2px;
  height: 8px;
  background: #7f8c8d;
  border-radius: 1px;
}

.boom-main-structure::after {
  content: '';
  position: absolute;
  top: -2px;
  right: 10px;
  width: 2px;
  height: 8px;
  background: #7f8c8d;
  border-radius: 1px;
}

/* 配重臂（向配重方向） */
.boom-counter-arm {
  position: absolute;
  top: 50%;
  right: 150px;
  transform: translateY(-50%);
  width: 60px;
  height: 6px;
  background: linear-gradient(to left, #7f8c8d, #95a5a6, #7f8c8d);
  border: 2px solid #5d6d7e;
  border-radius: 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.boom-counter-arm::before {
  content: '';
  position: absolute;
  top: 1px;
  left: 2px;
  right: 2px;
  height: 1px;
  background: linear-gradient(to left, rgba(255, 255, 255, 0.3), transparent, rgba(255, 255, 255, 0.3));
  border-radius: 1px;
}

.boom-counter-structure {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 45px;
  height: 3px;
  background: linear-gradient(to left, #95a5a6, #7f8c8d);
  border-radius: 1px;
}

.boom-counter-structure::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 5px;
  width: 1px;
  height: 5px;
  background: #7f8c8d;
  border-radius: 1px;
}

.boom-counter-structure::after {
  content: '';
  position: absolute;
  top: -1px;
  right: 5px;
  width: 1px;
  height: 5px;
  background: #7f8c8d;
  border-radius: 1px;
}

/* 斗轮装置 - 俯视图 */
.bucket-wheel-assembly {
  position: absolute;
  top: 50%;
  left: 100px;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
}

.bucket-wheel {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, #e67e22, #d35400, #a04000);
  border: 3px solid #7d3000;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);

  &.spinning {
    animation: bucketWheelSpin 2s linear infinite;
  }

  &.spinning-clockwise {
    animation: bucketWheelSpinClockwise 2s linear infinite;
  }

  &.spinning-counterclockwise {
    animation: bucketWheelSpinCounterclockwise 2s linear infinite;
  }
}

.bucket-wheel::before {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  right: 3px;
  bottom: 3px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), transparent 50%);
}

/* 斗仓旋转动画 */
@keyframes bucketWheelSpin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 顺时针旋转动画 */
@keyframes bucketWheelSpinClockwise {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 逆时针旋转动画 */
@keyframes bucketWheelSpinCounterclockwise {
  from { transform: rotate(0deg); }
  to { transform: rotate(-360deg); }
}

.wheel-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, #2c3e50, #1a252f);
  border-radius: 50%;
  border: 1px solid #0f1419;
}

.wheel-blades {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.blade {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 3px;
  background: linear-gradient(to right, #8b4513, #a0522d, #8b4513);
  border-radius: 1.5px;
  transform-origin: 0 50%;
  border: 1px solid #654321;
}

.blade::before {
  content: '';
  position: absolute;
  top: 0;
  right: -2px;
  width: 4px;
  height: 3px;
  background: #654321;
  border-radius: 0 2px 2px 0;
}

/* 配重区域 - 俯视图 */
.counterweight-area {
  position: absolute;
  top: 50%;
  right: 210px;
  transform: translateY(-50%);
  width: 20px;
  height: 25px;
}

.counterweight-block {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #34495e, #2c3e50, #1a252f);
  border: 2px solid #0f1419;
  border-radius: 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
}

.counterweight-block::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1px;
}

.counterweight-block::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, #555, #333);
  border-radius: 50%;
}

/* 状态指示器 */
.status-indicators {
  position: absolute;
  top: -12px;
  right: -8px;
  display: flex;
  align-items: center;
  gap: 4px;
  /* 确保不影响外部布局 */
  pointer-events: none;
  z-index: 10;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

  &.status-idle {
    background: #95a5a6;
  }

  &.status-working {
    background: #2ecc71;
    animation: statusPulse 1.5s infinite;
  }

  &.status-moving {
    background: #f39c12;
    animation: statusPulse 1s infinite;
  }

  &.status-maintenance {
    background: #e74c3c;
  }
}

@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

.operation-mode {
  background: rgba(46, 204, 113, 0.9);
  color: white;
  padding: 1px 4px;
  border-radius: 6px;
  font-size: 8px;
  font-weight: bold;
  white-space: nowrap;
}

/* 位置标签 */
.position-label {
  position: absolute;
  bottom: 55px;
  left: 40%;
  transform: translateX(-50%);
  background: rgba(52, 152, 219, 0.9);
  color: white;
  padding: 1px 6px;
  border-radius: 8px;
  font-size: 9px;
  font-weight: bold;
  white-space: nowrap;
  /* 确保不影响外部布局 */
  pointer-events: none;
  z-index: 10;
}

/* 工作目标指示 */
.target-indicator {
  position: absolute;
  top: -28px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(46, 204, 113, 0.9);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
  white-space: nowrap;

  &.pulsing {
    animation: targetPulse 2s infinite;
  }
}

@keyframes targetPulse {
  0%, 100% {
    opacity: 0.9;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 0.7;
    transform: translateX(-50%) scale(1.05);
  }
}

/* 详情面板 */
.details-panel {
  position: absolute; /* 相对于组件容器定位 */
  z-index: 1000; /* 确保显示在大机上方 */
  background: rgba(44, 62, 80, 0.95);
  color: white;
  border-radius: 8px;
  padding: 12px;
  min-width: 200px;
  max-width: 250px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  /* 确保面板不影响页面布局 */
  pointer-events: none; /* 防止鼠标事件干扰 */
  word-wrap: break-word;
  /* 添加动画效果 */
  animation: fadeInScale 0.2s ease-out;
  /* 允许内容正常换行 */
  white-space: normal;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);

  h4 {
    margin: 0;
    font-size: 14px;
    color: #3498db;
  }

  .status-text {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 4px;
    background: rgba(46, 204, 113, 0.8);
  }
}

.panel-content {
  .info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    font-size: 12px;

    span:first-child {
      color: #bdc3c7;
      margin-right: 8px;
    }

    span:last-child {
      color: white;
      font-weight: 500;
    }
  }
}

/* 尺寸变体 - 俯视图 */
.stacker-reclaimer.size-small {
  .device-body {
    width: 90px;
    height: 75px;
  }

  .rotating-platform {
    width: 75px;
    height: 60px;
  }

  .machine-house {
    width: 45px;
    height: 26px;
  }

  .boom-assembly {
    width: 105px;
    height: 22px;
    transform: translate(-33.33%, -50%);
    transform-origin: 33.33% 50%;
  }

  .boom-main-arm {
    width: 70px;
    height: 6px;
  }

  .boom-main-structure {
    width: 66px;
  }

  .boom-counter-arm {
    width: 35px;
    height: 4px;
  }

  .boom-counter-structure {
    width: 31px;
  }

  .bucket-wheel-assembly {
    width: 22px;
    height: 22px;
    left: 70px;
  }

  .counterweight-area {
    width: 15px;
    height: 18px;
    right: 35px;
  }
}

.stacker-reclaimer.size-large {
  .device-body {
    width: 150px;
    height: 125px;
  }

  .rotating-platform {
    width: 125px;
    height: 100px;
  }

  .machine-house {
    width: 75px;
    height: 44px;
  }

  .boom-assembly {
    width: 175px;
    height: 38px;
    transform: translate(-33.33%, -50%);
    transform-origin: 33.33% 50%;
  }

  .boom-main-arm {
    width: 117px;
    height: 10px;
  }

  .boom-main-structure {
    width: 112px;
  }

  .boom-counter-arm {
    width: 58px;
    height: 8px;
  }

  .boom-counter-structure {
    width: 53px;
  }

  .bucket-wheel-assembly {
    width: 38px;
    height: 38px;
    left: 117px;
  }

  .counterweight-area {
    width: 25px;
    height: 31px;
    right: 58px;
  }

  .wheel-center {
    width: 10px;
    height: 10px;
  }

  .blade {
    width: 15px;
    height: 4px;
  }
}

/* 动画效果 */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .details-panel {
    position: absolute;
    left: 50% !important;
    top: 100% !important;
    transform: translateX(-50%);
    margin-top: 8px;
  }
}
</style>
