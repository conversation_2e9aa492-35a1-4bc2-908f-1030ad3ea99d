<template>
  <div class="app-container" style="padding: 0px;">
    <el-form :model="parameter" ref="parameter" size="small" :inline="true" v-show="showSubmit" label-width="68px">
      <div style="margin-top: 5px;">
        <el-form-item label="班组" prop="teamGroup">
          <el-select v-model="teamGroup" placeholder="请选择">
            <el-option
              v-for="dict in dict.type.common_workGroup" :key="dict.value" :label="dict.label" :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班别" prop="teamClass">
          <el-select v-model="teamClass" placeholder="请选择">
            <el-option
              v-for="dict in dict.type.common_workClass" :key="dict.value" :label="dict.label" :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-button type="primary" size="mini" style="margin-left: 900px;">重置</el-button>
        <!--        <el-button type="primary" size="mini" >提交</el-button>-->
      </div>

      <div>
        <el-form-item label="铁次号" prop="ironNumber">
          <el-input v-model="ironNumber" placeholder="请输入内容" type="number"></el-input>
        </el-form-item>
        <!--        <el-button type="danger" size="mini" @click="removeItem(index)" style="margin-left: 122px;">删除罐号</el-button>-->
<!--        <el-button type="primary" size="mini" @click="addItem" style="margin-left: 122px;">新增罐号</el-button>-->
<!--        <el-button  type="danger" size="mini" @click="submitAddCan">提交</el-button>-->
      </div>

      <div class="scrollable-container">
        <el-card class="box-card" :body-style="{ padding: '10px' }">
          <el-button type="primary" size="mini" @click="addItem" style="margin-left: 758px;margin-bottom: 10px;">新增罐号</el-button>
<!--          <el-button  type="primary" size="mini" @click="submitAddCan">提交</el-button>-->
          <form @submit.prevent="submitForm">
            <button type="submit" class="submitBtn">提交铁次</button>
            <div v-for="(item, index) in formItems" :key="index">
              <el-form-item label="罐号" prop="canNumber" label-width="56px">
                <el-input v-model="item.canNumber" placeholder="请输入内容" style="width: 80px;" ></el-input>
              </el-form-item>
              <el-form-item label="出铁口" prop="ironSlogan" label-width="56px">
                <el-select v-model="item.ironSlogan" placeholder="请选择" style="width: 70px;" >
                  <el-option
                    v-for="dict in dict.type.blastFurnace_ironSlogan" :key="dict.value" :label="dict.label" :value="dict.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="估重(t)" prop="estimateWeight" label-width="56px">
                <el-input v-model="item.estimateWeight" placeholder="请输入内容" type="number" style="width: 100px;" ></el-input>
              </el-form-item>
              <el-form-item label="接铁开始" prop="canStartIronTime" label-width="68px" >
                <el-date-picker v-model="item.canStartIronTime" type="datetime" placeholder="选择日期时间" style="width: 150px;"
                                format="yyyyMMdd-HHmm" value-format="yyyyMMdd-HHmm" @click.native="canStartIronTimeOn"></el-date-picker>
              </el-form-item>
              <!--            @blur="handleChange"-->
              <el-form-item label="废钢加入量(吨)" prop="steelScrapAmount" label-width="107px" >
                <el-input v-model="item.steelScrapAmount" placeholder="请输入内容" style="width: 80px;" type="number"></el-input>
              </el-form-item>
              <el-button  type="danger" size="mini" @click="removeItem(index)">删除</el-button>
              <el-button  type="primary" size="mini" @click="submitAddCan(index)">提交</el-button>
            </div>
            <!--          <button type="button" @click="addItem">添加项</button>-->

          </form>
        </el-card>
      </div>

      <div class="right1" >
        <el-form-item label="出铁时间" prop="startIronTime" class="right1Form">
          <el-input v-model="startIronTime" placeholder="请输入内容" type="datetime"  @click.native="startIronTimeOn"></el-input>
        </el-form-item>
        <el-form-item label="见渣时间" prop="slagTime"class="right1Form">
          <el-input v-model="slagTime" placeholder="请输入内容" type="datetime" @click.native="slagTimeOn"></el-input>
        </el-form-item>
        <el-form-item label="堵口时间" prop="plugTime"class="right1Form">
          <el-input v-model="plugTime" placeholder="请输入内容" type="datetime" @click.native="plugTimeOn"></el-input>
        </el-form-item>
        <el-form-item label="料批" prop="batch"class="right1Form">
          <el-input v-model="parameter.batch" placeholder="请输入内容" type="number" style="width: 90px"></el-input>
        </el-form-item>
      </div>

      <div class="right2">
        <el-form-item label="铁间料" prop="ironMidMateral" class="right2Form">
          <el-input v-model="parameter.ironMidMateral" placeholder="请输入内容" type="number" :disabled="ironMidMateralFlag" ></el-input>
        </el-form-item>
        <el-form-item label="钻头直径" prop="bitDiameter" class="right2Form">
          <el-select v-model="parameter.bitDiameter" placeholder="请选择" >
            <el-option
              v-for="dict in dict.type.blastFurnace_bitDiameter" :key="dict.value" :label="dict.label" :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="铁口深度" prop="grooveDepth" class="right2Form">
          <el-input v-model="parameter.grooveDepth" placeholder="请输入内容" type="number" ></el-input>
        </el-form-item>
        <el-form-item label="处理" prop="handle" class="right2Form">
          <el-input v-model="parameter.handle" placeholder="请输入内容" type="text" style="width: 90px"></el-input>
        </el-form-item>
      </div>

      <div class="right3">
        <el-form-item label="打泥量" prop="amountMud" class="right3Form">
          <el-input v-model="parameter.amountMud" placeholder="请输入内容" type="number"  ></el-input>
        </el-form-item>
        <el-form-item label="打泥压力" prop="mudPressure" class="right3Form">
          <el-input v-model="parameter.mudPressure" placeholder="请输入内容" type="number"  ></el-input>
        </el-form-item>
        <el-form-item label="是否冒泥" prop="mudNot" class="right3Form">
          <el-select v-model="parameter.mudNot" placeholder="请选择" >
            <el-option
              v-for="dict in dict.type.blastFurnace_mudNot" :key="dict.value" :label="dict.label" :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="理论渣重" prop="theoreticalSlagWeight" class="right3Form">
          <el-input v-model="parameter.theoreticalSlagWeight" placeholder="请输入内容" type="number" style="width: 90px"></el-input>
        </el-form-item>
      </div>

      <div class="right4">
        <el-form-item label="实际渣量" prop="actualSlagWeight"class="right4Form" >
          <el-input v-model="parameter.actualSlagWeight" placeholder="请输入内容" type="number"  ></el-input>
        </el-form-item>
        <el-form-item label="炉渣状态" prop="slagStatus" class="right4Form">
          <el-select v-model="parameter.slagStatus" placeholder="请选择" >
            <el-option
              v-for="dict in dict.type.blastFurnace_slagStatus" :key="dict.value" :label="dict.label" :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="估硅" prop="estinameSi"class="right4Form" >
          <el-input v-model="parameter.estinameSi" placeholder="请输入内容" type="number"  ></el-input>
        </el-form-item>
      </div>

      <div class="right5">
        <el-form-item label="物理热1" prop="phtsicalHeat1" class="right5Form">
          <el-input v-model="parameter.phtsicalHeat1" placeholder="请输入内容" type="number"  ></el-input>
        </el-form-item>
        <el-form-item label="物理热2" prop="phtsicalHeat2" class="right5Form">
          <el-input v-model="parameter.phtsicalHeat2" placeholder="请输入内容" type="number"  ></el-input>
        </el-form-item>
        <el-form-item label="物理热3" prop="phtsicalHeat3" class="right5Form">
          <el-input v-model="parameter.phtsicalHeat3" placeholder="请输入内容" type="number"  ></el-input>
        </el-form-item>
      </div>

    </el-form>
    <hr style="background-color: #409eff; height: 1px;">
    <div class="block" style="margin-top:8px;margin-left: 20px;">
      <span >创建时间:</span>
      <el-date-picker style="margin-left: 10px;width: 392px;"
                      v-model="busiNessTimeArr" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
      </el-date-picker>
      <span style=" margin-left: 10px;">铁次号:</span>
      <el-input v-model="selectParam.ironNumber" placeholder="请输入内容" type="number" style="width: 200px;margin-left: 10px;"  clearable></el-input>
      <el-button type="primary" style="margin-left: 100px;" @click="handleQuery" size="mini">搜索</el-button>
    </div>
    <div style="margin-top: 7px;margin-left: 20px;margin-right: 20px;">
      <vxe-table
        border
        :edit-config="{trigger: 'click', mode: 'row'}"
        @edit-closed="editClosedEvent"
        :checkbox-config="{checkField: 'isChecked', indeterminateField: 'isIndeterminate'}"
        @checkbox-change="handleCheckboxChange"
        :data="tableData">
        <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
        <vxe-column field="businessTime" title="创建时间"  width="150" fixed="left" :edit-render="{ name: 'VxeDatePicker', props: { type: 'datetime' } }"></vxe-column>
        <vxe-column field="ironNumber" title="铁次号"  width='100' fixed="left" :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="startIronTime" title="出铁开始时间"  width='150' fixed="left" :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="slagTime" title="见渣时间" width='150' fixed="left"  :edit-render="{ name: 'VxeDatePicker', props: { type: 'datetime' } }"></vxe-column>
        <vxe-column field="plugTime" title="堵口时间" width='150' fixed="left" :edit-render="{ name: 'VxeDatePicker', props: { type: 'datetime' } }"></vxe-column>
        <vxe-column field="accumulateIronCapacity" title="累计铁量" width='100'  fixed="left"></vxe-column>
        <vxe-column field="batch" title="料批" width='80' :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="ironMidMateral" title="铁间料" width='80' ></vxe-column>
        <vxe-column field="bitDiameter" title="钻头" width='80' :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="grooveDepth" title="深度" width='80' :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="handle" title="处理" width='80'  :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="amountMud" title="打泥" width='80'  :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="mudPressure" title="打泥压力" width='100'  :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="mudNot" title="冒泥" width='80'  :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="theoreticalSlagWeight" title="理论渣量" width='100'  :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="actualSlagWeight" title="实际渣量" width='100'  :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="slagRate" title="见渣率" width='100' ></vxe-column>
        <vxe-column field="highestPhtsicalHeat" title="物理热" width='100'  ></vxe-column>
        <vxe-column field="slagStatus" title="炉渣状态" width='100'  :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="estinameSi" title="估硅" width='100'  ></vxe-column>
      </vxe-table>
      <vxe-pager
        :current-page.sync="mainTableConfig.pageConfig.pageNum"
        :page-size.sync="mainTableConfig.pageConfig.pageSize"
        :total="mainTableConfig.pageConfig.total"
        @page-change="pageChange">
      </vxe-pager>
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="罐重信息" name="罐重信息">
        <vxe-table
          border
          height="400"
          :edit-config="{trigger: 'click', mode: 'row'}"
          @edit-closed="editClosedEventWeightTable"
          :data="weightTableData">
          <vxe-column field="ironNumber" title="铁次号"  width='100'></vxe-column>
          <vxe-column field="canNumber1" title="罐号"  width='150' :edit-render="{name: 'input'}"></vxe-column>
          <vxe-column field="railway1" title="铁路" width='150'  ></vxe-column>
          <vxe-column field="estimateWeight1" title="估重" width='150' ></vxe-column>
          <vxe-column field="actualWeight1" title="实重" width='150' ></vxe-column>
        </vxe-table>
      </el-tab-pane>
      <el-tab-pane label="铁水成分" name="铁水成分">
        <vxe-table
          border
          height="400"
          :data="LiQuidIronTableData">
          <vxe-column field="ironNum" title="铁次号"  width='100'></vxe-column>
          <vxe-column field="sampleId" title="试样号"  width='150' ></vxe-column>
          <vxe-column field="si" title="Si" width='100'  ></vxe-column>
          <vxe-column field="mn" title="Mn" width='100' ></vxe-column>
          <vxe-column field="p" title="P" width='100' ></vxe-column>
          <vxe-column field="s" title="S" width='100' ></vxe-column>
          <vxe-column field="cr" title="Cr" width='100' ></vxe-column>
          <vxe-column field="ni" title="Ni" width='100' ></vxe-column>
          <vxe-column field="ti" title="Ti" width='100' ></vxe-column>
          <vxe-column field="asA" title="As" width='100' ></vxe-column>
        </vxe-table>
      </el-tab-pane>
      <el-tab-pane label="炉渣成分" name="炉渣成分">
        <vxe-table
          border
          height="400"
          :data="slagTableData">
          <vxe-column field="ironNum" title="铁次号"  width='100'></vxe-column>
          <vxe-column field="sampleId" title="试样号"  width='150' ></vxe-column>
          <vxe-column field="tiO2" title="TIO2" width='100'  ></vxe-column>
          <vxe-column field="feO" title="FEO" width='100' ></vxe-column>
          <vxe-column field="siO2" title="SIO2" width='100' ></vxe-column>
          <vxe-column field="al2O3" title="AL2O3" width='100' ></vxe-column>
          <vxe-column field="caO" title="CAO" width='100' ></vxe-column>
          <vxe-column field="mgO" title="MGO" width='100' ></vxe-column>
          <vxe-column field="s" title="S" width='100' ></vxe-column>
          <vxe-column field="k2O" title="K2O" width='100' ></vxe-column>
          <vxe-column field="na2O" title="NA2O" width='100' ></vxe-column>
          <vxe-column field="mnO" title="MNO" width='100' ></vxe-column>
          <vxe-column field="mgAl" title="MG/AL" width='100' ></vxe-column>
          <vxe-column field="r2" title="R2" width='100' ></vxe-column>
          <vxe-column field="r3" title="R3" width='100' ></vxe-column>
        </vxe-table>
      </el-tab-pane>
      <el-tab-pane label="物理热" name="物理热">
        <vxe-table
          border
          height="400"
          :edit-config="{trigger: 'click', mode: 'row'}"
          @edit-closed="editClosedEventPhtsicalHeat"
          :data="phtsicalHeatTableData">
          <vxe-column field="ironNumber" title="铁次号"  width='100'></vxe-column>
          <vxe-column field="phtsicalHeat1" title="物理热1"  width='150' :edit-render="{name: 'input'}"></vxe-column>
          <vxe-column field="phtsicalHeat2" title="物理热2" width='150'  :edit-render="{name: 'input'}"></vxe-column>
          <vxe-column field="phtsicalHeat3" title="物理热3" width='150' :edit-render="{name: 'input'}"></vxe-column>
          <vxe-column field="highestPhtsicalHeat" title="最高物理热" width='150' ></vxe-column>
        </vxe-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import {
    submitData,
    listIron,
    selectIronNumberCanWeight, // 罐重成分
    selectIronNumberPhtsicalHeat, // 物理热成分
    selectIronNumberLiQuidIron, // 铁水成分
    selectIronNumberSlag, // 炉渣成分
    ironEdit,
    insertIronWeight,
    canEdit,
  } from "@/api/report/preview/blastFurnace/iron";
  import dayjs from "dayjs";
  export default {
    name: "Iron",
    dicts: [
      'common_workGroup',
      'common_workClass',
      'blastFurnace_ironSlogan',
      'blastFurnace_bitDiameter',
      'blastFurnace_mudNot',
      'blastFurnace_slagStatus',
    ],
    data() {
      return {
        activeName:'罐重信息',
        mainTableConfig: {
          tableData: [],
          selectVO: '',
          pageConfig: {
            pageNum: 1, // 页码
            pageSize: 10, // 每页显示条目个数
            total: 0, // 总数
            background: true, // 是否展示分页器背景色
            pageSizes: [10, 20, 50, 100]// 分页器分页待选项
          }
        },
        formItems: [
          { canNumber: '',
            ironSlogan: '',
            estimateWeight:'',
            canStartIronTime: this.filterYMDhMS(new Date()),
            canEndIronTime:'',
            ironNumber:'',
            steelScrapAmount:'',
          }
        ],
        selectParam:{
          ironNumber:'',
          businessTimeStart:'',
          businessTimeEnd:'',
          blastFurnaceNumber:'',
        },
        // 业务时间
        busiNessTimeArr:[],
        // 显示提交条件
        showSubmit: true,
        parameter:{
          // 铁次号
          ironNumber:'',
          // 班组
          teamGroup:'',
          // 班别
          teamClass:'',
          // 罐1号
          canNumber1:'',
          // 出铁口
          ironSlogan:'',
          // 估重1
          estimateWeight1:'',
          // 铁间料
          ironMidMateral:'',
          // 钻头直径
          bitDiameter:'',
          // 铁口深度
          grooveDepth:'',
          // 处理
          handle:'钻',
          // 打泥量
          amountMud:'',
          // 打泥压力
          mudPressure:'',
          // 是否冒泥
          mudNot:'否',
          // 理论渣重
          theoreticalSlagWeight:'',
          // 实际渣重
          actualSlagWeight:'',
          // 炉渣状态
          slagStatus:'',
          // 物理热1
          phtsicalHeat1:'',
          // 物理热2
          phtsicalHeat2:'',
          // 物理热3
          phtsicalHeat3:'',
          // 开始时间
          startIronTime:'',
          // 见渣时间
          slagTime:'',
          // 堵口时间
          plugTime:'',
          // 高炉号
          blastFurnaceNumber:'',
          // 罐号 开始接铁时间
          canStartIronTime:'',
          // 罐号 结束接铁时间
          canEndIronTime:'',
          // 估硅
          estinameSi:'',
        },
        // 开始时间
        startIronTime:'',
        // 见渣时间
        slagTime:'',
        // 堵口时间
        plugTime:'',
        // 铁间料 第一次手动录入
        ironMidMateralFlag:false,
        ironNumber:'',
        teamGroup:'',
        teamClass:'',
        tableData:[],
        // 罐重信息
        weightTableData:[],
        // 物理热
        phtsicalHeatTableData:[],
        // 铁水成分
        LiQuidIronTableData:[],
        // 炉渣成分
        slagTableData:[],
        // 非多个禁用
        multiple: true,
        // 非单个禁用
        single: true,
        // 选中数组
        ids: [],
        canStartIronTime:'',
        canEndIronTime:'',
        // 是否提交罐重数据
        submitAddCanFlag:false,

      }
    },
    mounted() {
      window.addEventListener('scroll', this.handleScroll);
    },
    beforeDestroy() {
      window.removeEventListener('scroll', this.handleScroll);
    },
    created(){
      this.busiNessTimeArr.push(dayjs(new Date()).add(-1, "day"));
      this.busiNessTimeArr.push(dayjs(new Date()).add(1, "day"));
      // 高炉号
      let blastFurnaceNumber = this.getBlastFurnaceNumber();
      if(blastFurnaceNumber == 1){
        // 钻头直径
        this.parameter.bitDiameter = 65
        // 炉渣状态
        this.parameter.slagStatus = '1/4S'
      }else{
        // 钻头直径
        this.parameter.bitDiameter = 55
        // 炉渣状态
        this.parameter.slagStatus = '1/2S'
      }

      // 第一次进入页面 录入铁次号
      // 获取年的后两位
      let year = new Date().getFullYear().toString().slice(-2);
      // 获取月
      let month = new Date().getMonth()+1;
      if(month<10){
        month = '0'+month
      }
      // 获取日
      let date = new Date().getDate();
      date = date < 10 ? '0' + date : date
      this.ironNumber = "1"+this.getBlastFurnaceNumber()+year+month+date;

      // 展示页面数据
      this.queryLists()
    },
    methods:{
      getIronNum(){
        console.log("this.ironNumber1222:",this.ironNumber)
      },

      // 提交 罐重信息
      submitAddCan(index){
        console.log("index:",index)
        console.log("this.formItems:",this.formItems[index])
        if(this.ironNumber.length < 10){
          return  this.$modal.msgError("铁次号位数不正确");
        }
        if(this.formItems[index].canNumber == '' || this.formItems[index].canNumber == null){
          return this.$modal.msgError("罐号为空");
        }
        if(this.formItems[index].estimateWeight == '' || this.formItems[index].estimateWeight == null){
          return this.$modal.msgError("估重为空");
        }
        // for (let i = 0; i < this.formItems.length; i++) {
        //     if(this.formItems[i].canNumber == '' || this.formItems[i].canNumber == null){
        //       return this.$modal.msgError("罐号为空");
        //     }
        //     if(this.formItems[i].estimateWeight == '' || this.formItems[i].estimateWeight == null){
        //       return this.$modal.msgError("估重为空");
        //     }
        // }

        let arr=[]
        let arrchiled=[]
        let parArr = []
        let obj ={}
        let objParam ={}
        let arrParam = []
        obj.ironNumber = this.ironNumber
        // objParam.param = this.parameter
        parArr.push(obj)
        arrParam.push(this.parameter)
        //arr.push( this.formItems) // 计算得是 所有得罐
        arrchiled.push(this.formItems[index])
        arr.push(arrchiled)
        arr.push(parArr)
        arr.push(arrParam)
        this.submitAddCanFlag = true
        insertIronWeight(arr).then(response=>{
          this.$modal.msgSuccess("罐号数据提交成功");
          this.queryLists();
        })
      },

      /*  获取高炉号*/
      getBlastFurnaceNumber() {
        return this.$route.query.stoveNo;
      },
      // 开始出铁时间 获取本地时间
      startIronTimeOn() {
        this.startIronTime = this.filterYMDhMS(new Date())
        // this.$refs.xstartzuantimeMove.hidePicker()
      },
      // 见渣时间
      slagTimeOn(){
        this.slagTime = this.filterYMDhMS(new Date())
      },
      // 堵口时间
      plugTimeOn(){
        this.plugTime = this.filterYMDhMS(new Date())
      },
      canStartIronTimeOn(){
        this.canStartIronTime = (new Date())
      },

      addItem() {
        this.formItems.push({
          canNumber: '',
          ironSlogan: '',
          estimateWeight:'',
          canStartIronTime: this.filterYMDhMS(new Date()),
          canEndIronTime:'',
          ironNumber:'',
          steelScrapAmount:'', });
        console.log("this.formItems:",this.formItems)
      },
      removeItem(index) {
        this.formItems.splice( index, 1);
      },
      /*提交铁次按钮*/
      submitForm() {
        if(this.submitAddCanFlag == false){
          return  this.$modal.msgError("先提交罐重数据");
        }
        if(this.ironNumber.length < 10){
          return  this.$modal.msgError("铁次号位数不正确");
        }
        if(this.startIronTime ==''){
          return this.$modal.msgError("开始时间为空");
        }
        if(this.slagTime ==''){
          return this.$modal.msgError("见渣时间为空");
        }
        if(this.plugTime ==''){
          return this.$modal.msgError("堵口时间为空");
        }
        if(this.teamGroup ==''){
          return this.$modal.msgError("班组为空");
        }
        if(this.teamClass ==''){
          return this.$modal.msgError("班别为空");
        }
        let arr =[];
        let paramArr=[];
        this.parameter.blastFurnaceNumber = this.getBlastFurnaceNumber();
        // 开始时间
        this.parameter.startIronTime = this.changeTime(this.startIronTime)
        // 见渣时间
        this.parameter.slagTime = this.changeTime(this.slagTime)
        // 堵口时间
        this.parameter.plugTime = this.changeTime(this.plugTime)

        this.parameter.teamGroup = this.teamGroup
        this.parameter.teamClass = this.teamClass
        this.parameter.ironNumber = this.ironNumber
        this.parameter.canStartIronTime = this.canStartIronTime
        this.parameter.canEndIronTime = this.canEndIronTime
        arr.push(this.formItems) ;
        paramArr.push(this.parameter)
        arr.push(paramArr);
        submitData(arr).then(response=>{
          this.$modal.msgSuccess("提交成功");
          this.teamGroup = this.parameter.teamGroup
          this.teamClass = this.parameter.teamClass
          if(this.parameter.ironNumber != '' && this.parameter.ironNumber != null){

            // 1225041401
            console.log("this.parameter.ironNumber:",this.parameter.ironNumber)
           let yearStr = this.parameter.ironNumber.toString().substring(2,4) // 年的后两位
           let monthStr = this.parameter.ironNumber.toString().substring(4,6) // 月
           let dateStr = this.parameter.ironNumber.toString().substring(6,8) // 日
            console.log("yearStr:",yearStr)
            console.log("monthStr:",monthStr)
            console.log("dateStr:",dateStr)
            // 获取年的后两位
            let year = new Date().getFullYear().toString().slice(-2);
            // 获取月
            let month = new Date().getMonth()+1;
            if(month<10){
              month = '0'+month
            }
            // 获取日
            let date = new Date().getDate();
            let randomNum = "01";
            date = date < 10 ? '0' + date : date
            if( date != dateStr){
              this.ironNumber = "1"+blastFurnaceNumber+year+month+date+randomNum
            }else{
              this.ironNumber = Number(this.parameter.ironNumber) + 1
            }
          }
          this.clear()
          this.queryLists();
        })


      },

      /*搜索按钮*/
      handleQuery(){
        if (this.busiNessTimeArr.length == 2) {
          this.selectParam.businessTimeStart = dayjs(this.busiNessTimeArr[0]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
          this.selectParam.businessTimeEnd = dayjs(this.busiNessTimeArr[1]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        }
        this.queryLists();
        this.weightTableData=[];
        this.phtsicalHeatTableData = [];
        this.LiQuidIronTableData = [];
      },

      queryLists(){
        if (this.busiNessTimeArr.length == 2) {
          this.selectParam.businessTimeStart = dayjs(this.busiNessTimeArr[0]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
          this.selectParam.businessTimeEnd = dayjs(this.busiNessTimeArr[1]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        }
        this.selectParam.blastFurnaceNumber = this.getBlastFurnaceNumber();
        listIron(this.selectParam).then(response=>{
          this.tableData = response.rows
          this.mainTableConfig.pageConfig.total = response.total
        });
      },

      pageChange ({ pageSize, currentPage }) {
        this.mainTableConfig.pageConfig.pageNum = currentPage
        this.mainTableConfig.pageConfig.pageSize = pageSize
        this.selectParam.pageNum=this.mainTableConfig.pageConfig.pageNum
        this.selectParam.pageSize=this.mainTableConfig.pageConfig.pageSize
        this.queryLists()
      },

      /*关闭编辑框触发 失去焦点 进行保存数据*/
      editClosedEvent(row, column) {
        ironEdit(row.row).then(response=>{
          this.$modal.msgSuccess("编辑成功");
        });
      },
      /*关闭编辑框触发 失去焦点 进行保存数据 （物理热数据)*/
      editClosedEventPhtsicalHeat(row, column){
        ironEdit(row.row).then(response=>{
          this.$modal.msgSuccess("编辑成功");
        });
      },
      editClosedEventWeightTable(row, column){
        console.log("rowrow:",row.row)
        canEdit(row.row).then(response=>{
          this.$modal.msgSuccess("罐号编辑成功");
        });
      },

      /* 清空数据*/
      clear(){
        this.parameter={
          // 罐1号
          canNumber1:'',
          // 出铁口
          ironSlogan:'',
          // 估重1
          estimateWeight1:'',
          // 铁间料
          ironMidMateral:'',
          // 钻头直径
          bitDiameter:'',
          // 铁口深度
          grooveDepth:'',
          // 处理
          handle:'钻',
          // 打泥量
          amountMud:'',
          // 打泥压力
          mudPressure:'',
          // 是否冒泥
          mudNot:'否',
          // 理论渣重
          theoreticalSlagWeight:'',
          // 实际渣重
          actualSlagWeight:'',
          // 炉渣状态
          slagStatus:'',
          // 物理热1
          phtsicalHeat1:'',
          // 物理热2
          phtsicalHeat2:'',
          // 物理热3
          phtsicalHeat3:'',
          // 开始时间
          startIronTime:'',
          // 见渣时间
          slagTime:'',
          // 堵口时间
          plugTime:'',
        },
          this.startIronTime = '';
        this.slagTime = '';
        this.plugTime = '';
        if(this.getBlastFurnaceNumber() == 1){
          // 钻头直径
          this.parameter.bitDiameter = 65
          // 炉渣状态
          this.parameter.slagStatus = '1/4S'
        }else{
          // 钻头直径
          this.parameter.bitDiameter = 55
          // 炉渣状态
          this.parameter.slagStatus = '1/2S'
        }
        for (let i = 0; i < this.formItems.length; i++) {
          this.formItems[i].canNumber = '';
          this.formItems[i].estimateWeight = '';
          this.formItems[i].canEndIronTime = '';
          this.formItems[i].canStartIronTime = '';
          this.formItems[i].steelScrapAmount = '';
        }
        this.submitAddCanFlag = false;
      },
      // tab 切换
      handleClick(tab, event) {
        console.log(tab, event);
      },

      /*多选框触发事件*/
      handleCheckboxChange({ records, rowIndex, row }) {
        console.log("records:",records)
        this.ids = records.map(item => item.fid)
        if(this.ids.length>0){
          // 罐重数据
          selectIronNumberCanWeight(this.ids).then(response=>{
            this.weightTableData = response.data
          });
          // 物理热
          selectIronNumberPhtsicalHeat(this.ids).then(response=>{
            this.phtsicalHeatTableData = response.data
          });
          // 铁水成分
          selectIronNumberLiQuidIron(this.ids).then(response=>{
            console.log(" this.LiQuidIronTableData:",JSON.stringify( this.LiQuidIronTableData))
            this.LiQuidIronTableData = response.data

          });
          // 炉渣成分
          selectIronNumberSlag(this.ids).then(response=>{
            this.slagTableData = response.data
            console.log(" this.slagTableData:",JSON.stringify( this.slagTableData))
          });
        }
      },

      changeTime(time) {
        let splitElement = time.split('-')[0]
        let year = time.split('-')[0].substring(0, 4)
        let month = time.split('-')[0].substring(4, 6)
        let date = time.split('-')[0].substring(6, 8)
        let hour = time.split('-')[1].substring(0, 2)
        let minute = time.split('-')[1].substring(2, 4)
        return year + '-' + month + '-' + date + ' ' + hour + ':' + minute + ':00'
      },



      /* 中国标准时间转化为年月日时分秒*/
      filterYMDhMS(time) {
        var date = new Date(time)
        var y = date.getFullYear()
        var m = date.getMonth() + 1
        m = m < 10 ? '0' + m : m
        var d = date.getDate()
        d = d < 10 ? '0' + d : d
        var h = date.getHours()
        h = h < 10 ? '0' + h : h
        var minute = date.getMinutes()
        minute = minute < 10 ? '0' + minute : minute
        var s = date.getSeconds()
        s = s < 10 ? '0' + s : s
        return String(y) + String(m) + String(d) + '-' + String(h) + String(minute)
      },
    },

  }
</script>

<style lang="scss" scoped>
  .scrollable-container {
    /*width: 980px; !* 定义宽度 *!*/
    width: 1046px;
    height: 300px; /* 定义高度 */
    overflow: auto; /* 启用滚动条 */
  }
  .right1{
    margin-top: -359px;
    /*margin-left: 995px;*/
    margin-left: 1074px;
  }
  .right2{
    margin-left: 1074px;
  }
  .right3{
    margin-left: 1074px;
  }
  .right4{
    margin-left: 1074px;
  }
  .right5{
    margin-left: 1074px;
  }
  .right1Form{
    width: 130px;
  }
  .right2Form{
    width: 130px;
  }
  .right3Form{
    width: 130px;
  }
  .right4Form{
    width: 130px;
  }
  .right5Form{
    width: 130px;
  }
  .submitBtn{
    /*z-index: 1000;*/
    position: absolute;
    margin-left: 1330px;
    margin-top: -149px;
    width: 82px;
    height: 29px;
    background: #409eff;
    color: #ffffff;
    border: none;
    border-radius: 3px;
  }

  .selectIronNum{
    z-index: 1000;
    position: fixed;
    margin-left: 5px;
    width: 230px;
    margin-top: 2px;
  }
  .el-input el-input--suffix{
    width: 10px;
  }
</style>
