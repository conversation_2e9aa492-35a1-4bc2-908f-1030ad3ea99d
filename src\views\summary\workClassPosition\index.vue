<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="auto" size="small">
      <el-form-item label="开始时间">
        <el-date-picker v-model="dateRange" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期"
                        type="datetimerange"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="时间班组" prop="workClsass">
        <BWorkShiftSelect v-model="queryParams.workClsass" model-name="烧结排班"/>
      </el-form-item>
      <el-form-item label="管理班组" prop="workGroup">
        <BWorkClassSelect v-model="queryParams.workGroup" model-name="烧结排班"/>
      </el-form-item>

      <el-form-item>
        <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-edit"
          plain
          size="mini"
          type="success"
          @click="handleUpdate"
        >修改
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          icon="el-icon-delete"
          plain
          size="mini"
          type="danger"
          @click="handleDelete"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="queryList"></right-toolbar>
    </el-row>

    <div class="tableInfo">
      <vxe-grid
        ref="tableMainRef"
        header-align="center"
        :column-config="{resizable: true}"
        v-bind="mainTable.gridOptions"
        @radio-change="handleRadioChange"
      >
      </vxe-grid>

    </div>

    <el-tabs type="border-card" style="margin-top: 10px">
      <el-tab-pane>
        <template slot="label">
          仓位图 {{ this.refreshTime }}
        </template>
        <div class="tanks-container">
          <div v-for="(tank, index) in chartData.tanks" :key="index" class="tank-wrapper"
               :style="{ width: tank.width + 'px' }"
          >
            <!-- 料桶顶部 -->
            <div class="tanks-header">
              <div class="tanks-header-value" :style="{ background: tank.colorHeader }">{{ tank.value || '-' }}</div>
              <div class="tanks-header-label"></div>
            </div>
            <!-- 料桶主体 -->
            <div class="tank" :style="{ height: tank.height + 'px' }">
              <!-- 料桶填充 -->
              <div class="water" :style="{
                height: tank.percent + '%',
                backgroundColor: chartData.waterColor,
                opacity: chartData.waterOpacity
              }"
              ></div>

              <!-- 料桶信息 -->
              <div class="tank-info">
                <div>{{ tank.resultName1 }}</div>
                <div>{{ tank.percent + '%' }}</div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加或修改烧结物料消耗对话框 -->
    <el-dialog :title="editDialog.title" :visible.sync="editDialog.open" append-to-body width="700">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-row>
          <el-col :span="8">
            <el-form-item label="记账日期" prop="workDate">
              <el-date-picker v-model="form.workDate"
                              clearable
                              placeholder="请选择记账日期"
                              type="date"
                              value-format="yyyy-MM-dd"
                              style="width: 100%;"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="管理班组" prop="workGroup">
              <BWorkClassSelect v-model="form.workGroup" model-name="烧结排班"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="时间班组" prop="workClsass">
              <BWorkShiftSelect v-model="form.workClsass" model-name="烧结排班"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-tabs type="border-card">
          <el-tab-pane label="仓位清单">


            <vxe-table
              ref="editTableef"
              :column-config="{resizable: true}"
              :data="form.detailList"
              :height="300"
              :row-config="{isHover: true}"
              border
              header-align="center"
              stripe
            >
              <vxe-column title="仓位" width="auto">
                <template #default="{ row }">
                  <span v-if="row.resultCode1.indexOf('COL-') != -1">
                   交接班仓位
                  </span>
                  <span v-if="row.resultCode1.indexOf('INP-') != -1 && row.resultCode1.indexOf('FUEL') != -1">
                  燃料仓位
                  </span>
                  <span v-if="row.resultCode1.indexOf('INP-') != -1 && row.resultCode1.indexOf('FUEL') == -1">
                  其他
                  </span>
                </template>
              </vxe-column>
              <vxe-column field="resultName1" title="料仓" width="auto">
                <template #default="{ row }">
                  {{ row.resultName1.replace('交接班仓位—', '').replace('燃料仓位—', '燃料').replace('位', '') }}
                </template>
              </vxe-column>
              <vxe-column field="inputValue1" title="仓位" width="auto">
                <template #default="{ row }">
                  <el-input-number v-model="row.inputValue1" :min="0" :precision="2"
                                   style="margin-right: 5px ; margin-left: 5px;"
                  ></el-input-number>
                </template>
              </vxe-column>
            </vxe-table>

          </el-tab-pane>

        </el-tabs>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="editDialog.open=false">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>


import {
  deletePostion,
  getDefaultList,
  getEdit,
  getPostionReal,
  list,
  saveOrUpdate
} from '@/api/summary/workClassPosition'

import dayjs from 'dayjs'
import XEUtils from 'xe-utils'
import BWorkClassSelect from '@/components/BWorkClassSelect/index.vue'
import BWorkShiftSelect from '@/components/BWorkShiftSelect/index.vue'
import BMateSelect from '@/components/BMateSelect/index.vue'

export default {
  name: 'WorkClassPosition',
  components: { BMateSelect, BWorkShiftSelect, BWorkClassSelect },
  data() {
    return {
      refreshTimer: null, // 定义定时器变量
      refreshTime: null,
      tableHeight: 300,
      dateRange: [],
      prodCenterCode: null,
      reportUnitCode: null,
      chartData: {
        waterColor: '#3498db',
        waterOpacity: 0.7,
        tanks: [
          /** width：料桶宽度，height：高度，percent：高度百分比，value：当前值 */
          {
            width: 66,
            height: 88,
            percent: 45,
            value: 552.2,
            colorHeader: '#4E94E0',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 60,
            value: 552.2,
            colorHeader: '#4E94E0',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 36,
            value: 552.2,
            colorHeader: '#4E94E0',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 58,
            value: 552.2,
            colorHeader: '#4E94E0',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 72,
            value: 552.2,
            colorHeader: '#4E94E0',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 90,
            value: 552.2,
            colorHeader: '#4E94E0',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 45,
            value: 552.2,
            colorHeader: '#4E94E0',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 90,
            value: 552.2,
            colorHeader: '#4E94E0',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 36,
            value: 552.2,
            colorHeader: '#4E94E0',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 45,
            value: 552.2,
            colorHeader: '#4E94E0',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 76,
            value: 552.2,
            colorHeader: '#4E94E0',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 45,
            value: 552.2,
            colorHeader: '#4E94E0',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 76,
            value: 552.2,
            colorHeader: '#4E94E0',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 20,
            value: 552.2,
            colorHeader: '#4E94E0',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 45,
            value: 552.2,
            colorHeader: '#E87E8A',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 82,
            value: 552.2,
            colorHeader: '#E87E8A',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 49,
            value: 552.2,
            colorHeader: '#E87E8A',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          },
          {
            width: 66,
            height: 88,
            percent: 63,
            value: 552.2,
            colorHeader: '#E87E8A',
            type: '交接班',
            resultName1: '1#',
            resultCode1: 'COL-1'
          }
        ]
      },
      mainTable: {
        loading: true,
        single: true,
        multiple: true,
        selectId: [],
        selectedRadioRow: null,
        gridOptions: {
          border: true,
          stripe: false,
          loading: false,
          height: 400,
          columnConfig: {
            resizable: true
          },
          rowConfig: {
            isHover: true
          },
          radioConfig: {
            labelField: 'name',
            trigger: 'row'
          },
          columns: [
            { type: 'checkbox', width: 60 }
          ],
          data: [],
          masterIds: [],
          spanMethod({ row, rowIndex, column, visibleData }) {
            if (column.type === 'checkbox') {
              return
            }
            if (row.CONTENT_TYPE === '配比') {
              if (column.title === '变更原因') {
                return { rowspan: 2, colspan: 0 }
              }
            }

          }
        }
      },
      showSearch: true,
      editDialog: {
        title: '',
        // 是否显示弹出层
        open: false
      },
      // 查询参数
      queryParams: {
        dtStart: null,
        dtEnd: null,
        workDate: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null
      },
      // 表单参数
      form: {
        workDate: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null,
        detailList: []
      },
      // 表单校验
      rules: {
        workDate: [
          {
            required: true, message: '记账日期不能为空', trigger: 'blur'
          }
        ],
        workClsass: [
          {
            required: true, message: '时间班组不能为空', trigger: 'blur'
          }
        ],
        workGroup: [
          {
            required: true, message: '管理班组不能为空', trigger: 'blur'
          }
        ],
        beginTime: [
          {
            required: true, message: '开始时间不能为空 ', trigger: 'blur'
          }
        ]
      }
    }
  },
  created() {
    this.prodCenterCode = this.$route.query.prodCenterCode
    this.reportUnitCode = this.$route.query.reportUnitCode
    this.dateRange.push(dayjs(new Date()).startOf('date'))
    this.dateRange.push(dayjs(new Date()).endOf('date'))
    this.refreshChat()
    this.queryList()
    this.refreshTimer = setInterval(() => {
      var prodCenterCode = this.getProdCenterCode()
      if (prodCenterCode) {
        console.log('prodCenterCode', this.prodCenterCode)
        console.log('reportUnitCode', this.reportUnitCode)
        this.refreshChat()
      }
    }, 10000)
  },
  beforeDestroy() {
    // 在组件销毁前清除定时器
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
  },
  methods: {
    queryList() {
      if (this.dateRange.length == 2) {
        this.queryParams.dtStart = dayjs(this.dateRange[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.queryParams.dtEnd = dayjs(this.dateRange[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      }
      this.queryParams.prodCenterCode = this.getProdCenterCode()
      this.queryParams.reportUnitCode = this.getReportUnitCode()
      this.mainTable.gridOptions.columns = []
      this.mainTable.gridOptions.data = []
      this.mainTable.gridOptions.masterIds = []
      this.chartData.tanks = []
      this.mainTable.selectedRadioRow = null

      list(this.queryParams).then(response => {

        var colList = []
        var item1 = {
          title: '交接班仓位',
          children: []
        }
        var item2 = {
          title: '燃料仓位',
          children: []
        }
        var item3 = []

        response.data.col.forEach((item) => {
          if (item.key.indexOf('DETAIL') != -1) {
            if (item.key.indexOf('DETAIL_COL') != -1) {
              item1.children.push({
                field: item.key,
                title: item.value.replace('交接班仓位—', '').replace('仓', ''),
                width: 'auto',
                formatter({ cellValue }) {
                  if (cellValue == undefined) {
                    return 0
                  }
                  return Number(cellValue).toFixed(2)
                }
              })
            }
            if (item.key.indexOf('DETAIL_INP') != -1 && item.key.indexOf('FUEL') != -1) {
              item2.children.push({
                field: item.key,
                title: item.value.replace('燃料', '').replace('仓位', '').replace('料位-', ''),
                width: 'auto',
                formatter({ cellValue }) {
                  if (cellValue == undefined) {
                    return 0
                  }
                  return Number(cellValue).toFixed(2)
                }
              })
            }
            if (item.key.indexOf('DETAIL_INP') != -1 && item.key.indexOf('FUEL') == -1) {
              item3.push({
                field: item.key,
                title: item.value,
                width: 'auto',
                formatter({ cellValue }) {
                  if (cellValue == undefined) {
                    return 0
                  }
                  return Number(cellValue).toFixed(2)
                }
              })
            }
          }
        })
        colList.push({ type: 'radio', field: 'radio', width: 'auto', fixed: 'left' })
        colList.push({
          field: 'WORK_DATE',
          title: '工作日期',
          width: 'auto',
          visible: true,
          fixed: 'left',
          formatter({ cellValue }) {
            return XEUtils.toDateString(cellValue, 'yyyy-MM-dd')
          }
        })
        colList.push({
          field: 'WORK_CLSASS',
          title: '时间班组',
          width: 'auto',
          fixed: 'left',
          visible: true
        })

        colList.push({
          field: 'WORK_GROUP',
          title: '管理班组',
          width: 'auto',
          fixed: 'left',
          visible: true
        })

        colList.push(item1)
        colList.push(item2)
        item3.forEach((item) => {
          colList.push(item)
        })

        colList.push({
          field: 'UPDATE_BY',
          title: '更新者',
          width: 'auto',
          visible: true
        })
        colList.push({
          field: 'PROD_CENTER_NAME',
          title: '加工中心名称',
          width: 'auto',
          visible: true
        })
        colList.push({
          field: 'UPDATE_TIME',
          title: '更新时间',
          width: 'auto',
          visible: true
        })
        this.mainTable.gridOptions.columns = colList
        this.mainTable.gridOptions.data = response.data.rec
      })
    },

    refreshChat() {
      this.refreshTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')
      getPostionReal(this.getProdCenterCode()).then(res => {
        this.chartData.tanks = []
        res.data.forEach((item) => {
          if (item.type === '交接班') {
            this.chartData.tanks.push({
              width: 63,
              height: 88,
              percent: Number(item.percent).toFixed(2),
              value: Number(item.value).toFixed(2),
              colorHeader: '#4E94E0',
              type: '交接班',
              resultName1: item.resultName1.replace('交接班仓位—', '').replace('仓', '').replace('燃料', '').replace('位', '').replace('料-', ''),
              resultCode1: item.resultCode1
            })
          } else {
            this.chartData.tanks.push({
              width: 63,
              height: 88,
              percent: Number(item.percent).toFixed(2),
              value: Number(item.value).toFixed(2),
              colorHeader: '#E87E8A',
              type: '交接班',
              resultName1: item.resultName1.replace('交接班仓位—', '').replace('仓', '').replace('燃料', '').replace('位', '').replace('料-', ''),
              resultCode1: item.resultCode1
            })
          }

        })
      })
    },
    getProdCenterCode() {
      return this.prodCenterCode
    },
    getReportUnitCode() {
      return this.reportUnitCode
    },
    // 表单重置
    reset() {
      this.form = {
        workDate: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null,
        detailList: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryList()
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      var query = {
        prodCenterCode: this.getProdCenterCode(),
        reportUnitCode: this.getReportUnitCode()
      }
      getDefaultList(query).then(res => {
        this.form.detailList = res.data
        this.form.workDate = dayjs(new Date()).format('YYYY-MM-DD')
        this.editDialog.open = true
        this.editDialog.title = '添加交接班仓位信息'
      })
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const selectedRows = this.mainTable.selectedRadioRow
      if (selectedRows == null) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
      }
      var queryPar = {
        masterId: selectedRows.MASTER_ID,
        prodCenterCode: this.getProdCenterCode(),
        reportUnitCode: this.getReportUnitCode()
      }
      getEdit(queryPar).then(res => {
        this.form = res.data
        this.editDialog.open = true
        this.editDialog.title = '修改交接班仓位信息'
      })

    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (this.form.detailList.length == 0) {
          this.$message({
            message: '请填写仓位信息',
            type: 'warning'
          })
          return
        }
        var formPar = JSON.parse(JSON.stringify(this.form))

        if (valid) {
          formPar.prodCenterCode = this.getProdCenterCode()
          formPar.reportUnitCode = this.getReportUnitCode()
          saveOrUpdate(formPar).then(response => {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.editDialog.open = false
            this.queryList()
          })
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const selectedRows = this.mainTable.selectedRadioRow
      if (selectedRows == null) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
      }
      var queryPar = {
        masterId: selectedRows.MASTER_ID,
        prodCenterCode: this.getProdCenterCode(),
        reportUnitCode: this.getReportUnitCode()
      }
      this.$modal
        .confirm('是否确认删除？')
        .then(function() {
          return deletePostion(queryPar)
        })
        .then(() => {
          this.queryList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    },

    /** 导出按钮操作 */
    handleExport() {
      const $table = this.$refs.tableMainRef
      if ($table) {
        $table.exportData({
          type: 'csv',
          filename: '交接班仓位', // 文件名
          sheetName: 'Sheet1' // sheet名称
        })
      }
    },
    handleRadioChange({ row }) {
      this.mainTable.selectedRadioRow = row
      this.refreshChat(this.mainTable.selectedRadioRow.MASTER_ID)
    }
  },
  mounted() {
    this.$nextTick(() => {
      let topValue = document.getElementsByClassName('tableInfo')[0].getBoundingClientRect().top
      this.tableHeight = document.body.clientHeight - topValue
      this.mainTable.gridOptions.height = this.tableHeight - 220
    })
  }
}
</script>
<style scoped>
.tank-app {
  font-family: Arial, sans-serif;
  max-width: 86%;
  margin: 0 auto;
  padding: 20px;
}

.tanks-header {
  height: 28px;
  width: 100%;
}

.tanks-header-value {
  font-size: 14px;
  height: 80%;
  width: 100%;
  text-align: center;
  color: #fff;
  line-height: 22px;
}

.tanks-header-label {
  height: 20%;
  width: 50%;
  position: relative;
  left: 25%;
  background: #E87E8A;
}

.tanks-container {
  display: flex;
  justify-content: left;
  align-items: flex-end;
  gap: 20px;
  flex-wrap: wrap;
}

.tank-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tank {
  position: relative;
  width: 100%;
  border: 1px solid #4E94E0;
  border-top-left-radius: 0% 20px;
  border-top-right-radius: 0% 20px;
  background-color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.1);
}

.water {
  position: absolute;
  bottom: 0;
  width: 100%;
  overflow: hidden;
  transition: height 0.5s ease;
}

.tank-bottom {
  height: 15px;
  background-color: #555;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  margin-top: -2px;
}

.tank-label {
  margin-top: 10px;
  font-weight: bold;
  color: #555;
}

.tank-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.3);
  padding: 5px;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  pointer-events: none;
}

.water::after {
  content: "";
  position: absolute;
  top: -5px;
  left: 0;
  right: 0;
  height: 10px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: wave 3s infinite linear;
}

@keyframes wave {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@media (max-width: 768px) {
  .tanks-container {
    flex-wrap: wrap;
    gap: 20px;
  }

  .tank-wrapper {
    width: calc(50% - 20px);
  }
}
</style>
</style>
