<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    width="500px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="stockForm"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="left"
    >
      <!-- 物料信息显示 -->
      <div class="material-info-section">
        <h4>物料信息</h4>
        <div class="info-row">
          <span class="label">物料名称：</span>
          <span class="value">{{ materialInfo.materialName || '未知物料' }}</span>
        </div>
        <div class="info-row">
          <span class="label">当前重量：</span>
          <span class="value">{{ materialInfo.materialWeight || 0 }}T</span>
        </div>
        <div class="info-row">
          <span class="label">位置信息：</span>
          <span class="value">{{ materialInfo.position || '未知位置' }}</span>
        </div>
      </div>

      <!-- 操作表单 -->
      <el-form-item label="操作重量" prop="weight">
        <el-input-number
          v-model="formData.weight"
          :min="0.1"
          :max="maxWeight"
          :precision="2"
          :step="0.1"
          placeholder="请输入重量"
          style="width: 100%"
        >
          <template slot="append">T</template>
        </el-input-number>
        <div class="weight-tips">
          <span v-if="operationType === 'out'">
            最大可出库：{{ materialInfo.materialWeight || 0 }}T
          </span>
          <span v-else>
            请输入入库重量
          </span>
        </div>
      </el-form-item>


    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button
        type="primary"
        @click="handleSubmit"
        :loading="submitting"
      >
        确认{{ operationType === 'in' ? '入库' : '出库' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { stockInOrOut } from '@/api/wms/storehouse'

export default {
  name: 'StockOperationDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    operationType: {
      type: String,
      default: 'in', // 'in' 入库, 'out' 出库
      validator: value => ['in', 'out'].includes(value)
    },
    materialInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      submitting: false,
      formData: {
        weight: null
      },
      formRules: {
        weight: [
          { required: true, message: '请输入操作重量', trigger: 'blur' },
          { type: 'number', min: 0.1, message: '重量必须大于0.1T', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.operationType === 'in' ? '物料入库' : '物料出库'
    },
    maxWeight() {
      if (this.operationType === 'out') {
        return parseFloat(this.materialInfo.materialWeight) || 0
      }
      return 9999 // 入库时的最大重量限制
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetForm()
      }
    }
  },
  methods: {
    // 重置表单
    resetForm() {
      this.formData = {
        weight: null
      }
      if (this.$refs.stockForm) {
        this.$refs.stockForm.clearValidate()
      }
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    // 提交表单
    async handleSubmit() {
      try {
        // 表单验证
        await this.$refs.stockForm.validate()

        // 出库时检查重量
        if (this.operationType === 'out') {
          const currentWeight = parseFloat(this.materialInfo.materialWeight) || 0
          // if (this.formData.weight > currentWeight) {
          //   this.$message.error('出库重量不能超过当前库存重量')
          //   return
          // }
        }

        this.submitting = true

        // 提交数据
        const submitData = {
          stockRealId: this.materialInfo.stockRealId, // 实际库存ID
          stackPlanId: this.materialInfo.stackPlanId, // 计划ID
          weight: this.formData.weight,
          remark: this.operationType === 'in' ? '入库' : '出库' 
        }

        console.log('最终提交数据:', submitData);

        // 出入库API
        const response = await stockInOrOut(submitData)

        if (response && response.code === 200) {
          this.$message.success(`${this.operationType === 'in' ? '入库' : '出库'}操作成功`)
          this.$emit('success', {
            type: this.operationType,
            data: submitData,
            response: response
          })
          this.handleClose()
        }

      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.material-info-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;

  h4 {
    margin: 0 0 12px 0;
    color: #303133;
    font-size: 14px;
    font-weight: 600;
  }

  .info-row {
    display: flex;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      color: #606266;
      font-size: 13px;
      min-width: 80px;
    }

    .value {
      color: #303133;
      font-size: 13px;
      font-weight: 500;
    }
  }
}

.weight-tips {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

// 表单样式优化
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-textarea__inner) {
  resize: none;
}
</style>
