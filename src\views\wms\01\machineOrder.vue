<template>
    <div style="margin: 10px;">
        <vxe-grid ref="tableMainRef" v-bind="gridOptions" style="margin: 10px;" @page-change="pageChangeEvent">
            <template #form>
                <vxe-form ref="formRef" v-bind="formOptions">
                    <template #action>
                        <vxe-button status="primary" @click="searchEvent">搜索</vxe-button>
                        <vxe-button @click="resetEvent">重置</vxe-button>
                    </template>
                </vxe-form>
            </template>
            <template #toolbarButtons>
                <vxe-button status="primary" @click="addEvent">新增</vxe-button>
                <vxe-button status="primary" @click="editEvent">编辑</vxe-button>
                <vxe-button status="primary" @click="handleDelete">删除</vxe-button>
            </template>
        </vxe-grid>

        <!-- 新增/编辑弹窗 -->
        <vxe-modal v-model="showModal" :title="modalTitle" width="600" :mask-closable="false">
            <vxe-form ref="modalFormRef" v-bind="modalFormOptions">
                <template #action>
                    <div style="text-align: center; margin-top: 20px">
                        <vxe-button status="primary" @click="submitEvent">提交</vxe-button>
                        <vxe-button @click="cancelEvent">取消</vxe-button>
                    </div>
                </template>
            </vxe-form>
        </vxe-modal>
    </div>
</template>

<script>
import {
    selectMachineData,
    insertMachineWorkData,
    updateMachineData,
    deleteMachineData
} from "@/api/wms/machine";

import { debounce } from 'lodash';

import {
    initMaterialList
} from "@/api/wms/stocktransfer";
import { event } from "jquery";

export default {
    name: 'machineData',
    data() {
        // 料号选择器配置
        const materialEditRender = {
            name: 'VxeSelect',
            props: {
                filterable: true,
                clearable: true,
            },
            options: [],
            events: {
                change: this.changeMate
            }
        }

        // 料条选择器配置
        const crossRegionRender = {
            name: 'VxeSelect',
            props: {
                clearable: true,
            },
            options: [],
            events: {
                change: this.changeCrossRegion
            }
        }

        // 垛位选择器配置
        const stackingPositionRender = {
            name: 'VxeSelect',
            props: {
                clearable: true,
            },
            options: [],
        }

        //大机名称选择器配置
        const machineNameRender = {
            name: 'VxeSelect',
            props: {
                clearable: true,
            },
            options: [
                { label: '1号堆取料机', value: '1号堆取料机' },
                { label: '2号取料机', value: '2号取料机' },
                { label: '3号堆取料机', value: '3号堆取料机' },
                { label: '4号取料机', value: '4号取料机' }
            ],
            events: {
                change: this.changeMachineName
            }
        }

        const instructionRender = {
            name: 'VxeSelect',
            props: {
                clearable: true,
            },
            options: [
                { label: '堆料', value: '堆料' },
                { label: '取料', value: '取料' }
            ]
        }

        return {
            materialEditRender,
            crossRegionRender,
            stackingPositionRender,
            showModal: false,
            modalTitle: '新增',
            isEdit: false,
            currentRow: null,
            allMaterialInfoList: [],
            gridOptions: {
                columns: [],
                data: [],

                height: 800,
                border: true,
                stripe: true,
                align: 'center',

                columnConfig: {
                    resizable: true
                },
                rowConfig: {
                    isHover: true,
                    isCurrent: true,
                },

                pagerConfig: {
                    total: 0,
                    currentPage: 1,
                    pageSize: 10,
                },

                editConfig: {
                    trigger: 'dblclick',
                    mode: 'cell',
                },

                toolbarConfig: {
                    custom: true,
                    zoom: true,
                    slots: { buttons: "toolbarButtons" },
                },
                customConfig: {
                    immediate: true
                },
            },
            formOptions: {
                data: {
                    machineName: '',
                    status: ''
                },
                items: [
                    { field: 'machineName', title: '大机名称', itemRender: { name: 'VxeInput' } },
                    {
                        field: 'status', title: '状态', itemRender: {
                            name: 'VxeSelect',
                            props: {
                                clearable: true
                            },
                            options: [
                                { label: '未执行', value: 0 },
                                { label: '正在执行', value: 1 },
                                { label: '已完成', value: 2 }
                            ]
                        }
                    },
                    { slots: { default: 'action' } }
                ]
            },
            modalFormOptions: {
                data: {
                    machineName: '',
                    instruction: '',
                    mateCode: '',
                    mateName: '',
                    stackingPosition: '',
                    crossRegion: '',
                    status: 0
                },
                titleAlign: 'right',
                titleWidth: 100,
                items: [
                    {
                        field: 'machineName',
                        title: '大机名称',
                        itemRender: machineNameRender,
                        span: 24,
                        required: true
                    },
                    {
                        field: 'instruction',
                        title: '指令',
                        itemRender: instructionRender,
                        span: 24,
                        required: true
                    },
                    {
                        field: 'mateCode',
                        title: '物料',
                        itemRender: materialEditRender,
                        span: 24,
                        required: true
                    },

                    {
                        field: 'crossRegion',
                        title: '料条',
                        itemRender: crossRegionRender,
                        span: 24,
                        required: true
                    },

                    {
                        field: 'stackingPosition',
                        title: '垛位',
                        itemRender: stackingPositionRender,
                        span: 24,
                        required: true
                    },

                    {
                        field: 'status',
                        title: '状态',
                        itemRender: {
                            name: 'VxeSelect',
                            options: [
                                { label: '未执行', value: 0 },
                                { label: '正在执行', value: 1 },
                                { label: '已完成', value: 2 }
                            ]
                        },
                        span: 24,
                        required: true
                    },
                    { slots: { default: 'action' }, span: 24 }
                ]
            }
        }
    },

    mounted() {
        this.initGridData();
        this.initMaterialData();
    },

    methods: {

        initGridData() {
            this.gridOptions.columns = [
                { type: 'checkbox', width: 50 },
                { type: 'seq', width: 50 },
                { field: 'machineName', title: '大机名称', },
                { field: 'instruction', title: '指令', },
                { field: 'mateName', title: '物料', },
                { field: 'crossRegion', title: '垛位', },
                { field: 'stackingPosition', title: '料条', },
                {
                    field: 'status',
                    title: '状态',
                    formatter: ({ cellValue }) => {
                        const statusMap = {
                            0: '未执行',
                            1: '正在执行',
                            2: '已完成'
                        };
                        return statusMap[cellValue] || cellValue;
                    }
                },
            ]
            this.handlePageData()
        },

        async searchEvent() {
            this.gridOptions.pagerConfig.currentPage = 1;
            this.handlePageData()
        },

        resetEvent() {
            this.$refs.formRef.reset();
            this.searchEvent();
        },

        getStoreHouseCode() {
            return this.$route.query.storehouseCode
        },

        handlePageData() {
            this.gridOptions.loading = true
            this.formOptions.data.storehouseCode = this.getStoreHouseCode();
            selectMachineData(this.formOptions.data).then(response => {
                let data = response.data
                const { pageSize, currentPage } = this.gridOptions.pagerConfig
                this.gridOptions.pagerConfig.total = data.length
                this.gridOptions.data = data.slice((currentPage - 1) * pageSize, currentPage * pageSize)
                this.gridOptions.loading = false
            })
        },

        pageChangeEvent({ pageSize, currentPage }) {
            this.gridOptions.pagerConfig.currentPage = currentPage;
            this.gridOptions.pagerConfig.pageSize = pageSize;
            this.handlePageData();
        },

        addEvent() {
            this.showModal = true;
            this.modalTitle = '新增';
            this.isEdit = false;
            this.currentRow = null;
            this.resetModalFrom();
            // this.$nextTick(() => {
            //     this.$refs.modalFormRef.reset();
            //     this.modalFormOptions.data.machineName = '';
            //     this.modalFormOptions.data.instruction = '';
            //     this.modalFormOptions.data.mateCode = '';
            //     this.modalFormOptions.data.mateName = '';
            // });
        },

        editEvent() {
            const selectedRows = this.$refs.tableMainRef.getCheckboxRecords();
            if (selectedRows.length !== 1) {
                this.$message.warning('请选择一条记录进行编辑');
                return;
            }
            const selectedRow = selectedRows[0];
            if (selectedRow.status === 1 || selectedRow.status === 2) {
                const statusText = selectedRow.status === 1 ? '正在执行' : '已完成';
                this.$message.warning(`该记录状态为"${statusText}"，不可编辑`);
                return;
            }
            this.currentRow = selectedRow;
            this.showModal = true;
            this.modalTitle = '编辑';
            this.isEdit = true;
            this.$nextTick(() => {
                this.modalFormOptions.data = { ...this.currentRow };
                this.onMaterialChange({ value: this.currentRow.mateCode });
            });
        },

        clearSelectRow() {
            this.selectedRows = [];
            this.$refs.tableMainRef.setCheckboxRow([]);
        },

        // 删除处理
        handleDelete() {
            const selectedRows = this.$refs.tableMainRef.getCheckboxRecords();
            if (selectedRows == null || selectedRows.length == 0) {
                this.$message({
                    message: "请选择要删除的数据",
                    type: "warning",
                });
                return;
            }
            const invalidRows = selectedRows.filter(row =>
                row.status === 1 || row.status === 2
            );
            if (invalidRows.length > 0) {
                const invalidNames = invalidRows.map(row => {
                    const statusText = row.status === 1 ? '正在执行' : '已完成';
                    return `${row.machineName}(${statusText})`;
                }).join(", ");
                this.$message({
                    message: `大机名称为 ${invalidNames} 的数据状态不为未执行，不可删除`,
                    type: "warning",
                });
                return;
            }

            this.$modal
                .confirm("是否确认删除？")
                .then(() => {
                    const ids = selectedRows.map((row) => row.id);
                    return deleteMachineData(ids);
                })
                .then(() => {
                    this.handlePageData();
                    this.$modal.msgSuccess("删除成功");
                })
                .catch(() => {
                    this.$modal.msgError("删除失败");
                });
        },

        // 新增/编辑弹窗提交
        submitEvent: debounce(function () {
            this.$refs.modalFormRef.validate().then(() => {
                const formData = { ...this.modalFormOptions.data };
                const apiPromise = this.isEdit
                    ? updateMachineData(formData)
                    : insertMachineWorkData(formData);
                apiPromise.then(() => {
                    this.$message.success(this.isEdit ? '更新成功' : '新增成功');
                    this.showModal = false;
                    this.handlePageData();
                }).catch(error => {
                    console.error(this.isEdit ? '更新失败' : '新增失败', error);
                    this.$message.error(this.isEdit ? '更新失败，请重试' : '新增失败，请重试');
                });
            });
        }, 1000, { leading: true, trailing: false }),

        cancelEvent() {
            this.showModal = false;
        },

        // 初始化物料数据
        async initMaterialData() {
            try {
                const materialResponse = await initMaterialList();
                console.log('物料数据响应:', materialResponse);
                let materialData = materialResponse.data;
                console.log('物料数据:', materialData);
                // 过滤掉非原料库1#料场的物料
                materialData = materialData.filter(item => item.storehouseName === "原料库1#料场");
                console.log('过滤后的物料数据:', materialData);
                this.allMaterialInfoList = materialData.map(item => ({
                    value: `${item.materialNumber}`,
                    label: `${item.materialName}`,
                    crossRegion: `${item.crossRegion}`,
                    stackingPosition: `${item.stackingPosition}`
                }));
                const uniqueMaterialNames = [...new Set(materialData.map(item => item.materialName))];
                const uniqueMaterialOptions = uniqueMaterialNames.map(name => {
                    const firstItem = materialData.find(item => item.materialName === name);
                    return {
                        value: `${firstItem.materialNumber}`,
                        label: `${name}`
                    };
                });
                this.materialEditRender.options = uniqueMaterialOptions;
            } catch (error) {
                this.$modal.msgError("初始化数据失败");
                console.error("初始化数据失败:", error);
            }
        },
        resetModalFrom() {
            this.$nextTick(() => {
                this.$refs.modalFormRef.reset();
                this.modalFormOptions.data = {
                    machineName: '',
                    instruction: '',
                    mateCode: '',
                    mateName: '',
                    stackingPosition: '',
                    crossRegion: '',
                    status: 0
                };
                this.crossRegionRender.options = [];
                this.stackingPositionRender.options = [];
            }); this.$nextTick(() => {
                this.$refs.modalFormRef.reset();
                this.modalFormOptions.data = {
                    machineName: '',
                    instruction: '',
                    mateCode: '',
                    mateName: '',
                    stackingPosition: '',
                    crossRegion: '',
                    status: 0
                };
                this.crossRegionRender.options = [];
                this.stackingPositionRender.options = [];
            });
        },

        changeMate() {
            if (this.materialEditRender.options.length > 0) {
                this.updateMaterialName();
            } else {
                this.$watch(
                    () => this.materialEditRender.options.length,
                    (newLength) => {
                        if (newLength > 0) {
                            this.updateMaterialName();
                        }
                    }
                );
            }
        },

        updateMaterialName() {
            console.log('更新物料编码:', this.modalFormOptions.data.mateCode);
            const materialOption = this.materialEditRender.options.find(
                option => option.value === this.modalFormOptions.data.mateCode
            );
            if (materialOption) {
                this.modalFormOptions.data.mateName = materialOption.label;
            }
            const materialItems = this.allMaterialInfoList.filter(
                item => item.value === this.modalFormOptions.data.mateCode
            );
            // 提取唯一的料条和垛位
            const uniqueCrossRegions = [...new Set(materialItems.map(item => item.crossRegion))];
            const uniqueStackingPositions = [...new Set(materialItems.map(item => item.stackingPosition))];
            console.log('料条:', uniqueCrossRegions);
            console.log('垛位:', uniqueStackingPositions);

            // 更新料条下拉框
            this.crossRegionRender.options = uniqueCrossRegions.map(region => ({
                value: region,
                label: region,
            }));

            // 更新垛位下拉框
            this.stackingPositionRender.options = uniqueStackingPositions.map(position => ({
                value: position,
                label: position
            }));

            if (uniqueCrossRegions.length === 1) {
                this.modalFormOptions.data.crossRegion = uniqueCrossRegions[0];
            } else {
                this.modalFormOptions.data.crossRegion = '';
            }

            if (uniqueStackingPositions.length === 1) {
                this.modalFormOptions.data.stackingPosition = uniqueStackingPositions[0];
            } else {
                this.modalFormOptions.data.stackingPosition = '';
            }
        },
        changeCrossRegion() {
            console.log('更新料条:', this.modalFormOptions.data.crossRegion);
            //根据更新的物料和料条找到对应的垛位
            const materialItems = this.allMaterialInfoList.filter(
                item => item.value === this.modalFormOptions.data.mateCode && item.crossRegion === this.modalFormOptions.data.crossRegion
            );
            const uniqueStackingPositions = [...new Set(materialItems.map(item => item.stackingPosition))];
            console.log('更新后的垛位:', uniqueStackingPositions);
            this.stackingPositionRender.options = uniqueStackingPositions.map(position => ({
                value: position,
                label: position
            }));
            if (uniqueStackingPositions.length === 1) {
                this.modalFormOptions.data.stackingPosition = uniqueStackingPositions[0];
            } else {
                this.modalFormOptions.data.stackingPosition = '';
            }
        },

        changeMachineName() {
            console.log('更新大机名称:', this.modalFormOptions.data.machineName);
            const machineName = this.modalFormOptions.data.machineName;
            let instruction = '';
            if (machineName === '1号堆取料机') {
            } else if (machineName === '2号取料机') {
                instruction = '取料';
            } else if (machineName === '3号堆取料机') {
            } else if (machineName === '4号取料机') {
                instruction = '取料';
            }
            this.modalFormOptions.data.instruction = instruction;
        }
    }
}
</script>