<template>
  <div class="app-container" style="padding: 0px;">
    <div class="block" >
      <span >取样时间:</span>
      <el-date-picker style="margin-left: 10px;width: 392px;"
                      v-model="publishTimeArr" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
      </el-date-picker>
      <el-button type="primary" style="margin-left: 100px;" @click="handleQuery" size="mini">搜索</el-button>
    </div>
    <div>
      <vxe-table
        align="center"
        border
        height="800"
        :data="tableData">
        <vxe-column type="seq" width="70"></vxe-column>
        <vxe-column field="samplingTime" title="取样时间"></vxe-column>
        <vxe-column field="mateName" title="物料名称"></vxe-column>
        <vxe-column field="workClass" title="时间" width="auto"></vxe-column>
        <vxe-column field="c固" title="C固"></vxe-column>
        <vxe-column field="a" title="A"></vxe-column>
        <vxe-column field="v" title="V"></vxe-column>
        <vxe-column field="s" title="S"></vxe-column>
        <vxe-column field="h2O" title="H20"></vxe-column>
        <vxe-column field="粒度_200目" title="-200目"></vxe-column>
      </vxe-table>
    </div>

  </div>
</template>

<script>
  import {
    coalIndustryElementList,
  } from "@/api/report/preview/blastFurnace/coalIndustry";
  import dayjs from "dayjs";
  export default {
    name: "coalIndustry",
    data(){
      return{
        loading:true,
        tableData:[],
        publishTimeArr:[],
        selectParam:{
          startTime:'',
          endTime:'',
          prodCenterCode:'',
        }

      }
    },
    created() {
      this.publishTimeArr.push(dayjs(new Date()).add(-1, "day"));
      this.publishTimeArr.push(dayjs(new Date()).add(1, "day"));

      this.queryList();
    },
    methods:{
      /* 获取路径高炉号*/
      getProdCenterCode(){
        return this.$route.query.prodCenterCode;
      },

      /*搜索按钮*/
      handleQuery(){
        if(this.publishTimeArr != null){
          if (this.publishTimeArr.length == 2) {
            this.selectParam.startTime = dayjs(this.publishTimeArr[0]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.selectParam.endTime = dayjs(this.publishTimeArr[1]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }
        }
        this.queryList();
      },

      /* 查询数据列表 */
      queryList(){
        if(this.publishTimeArr != null){
          if (this.publishTimeArr.length == 2) {
            this.selectParam.startTime = dayjs(this.publishTimeArr[0]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.selectParam.endTime = dayjs(this.publishTimeArr[1]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }
        }
        this.selectParam.prodCenterCode = this.getProdCenterCode();
        coalIndustryElementList(this.selectParam).then(response=>{
          this.tableData = response.data
          this.loading = false
          console.log("response:",JSON.stringify(response))
        });
      }

    }
  }
</script>

<style scoped>
  .block{
    margin-top: 10px;
    margin-bottom: 10px;
    margin-left: 10px;
  }
</style>
