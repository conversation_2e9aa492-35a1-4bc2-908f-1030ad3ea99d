<template>
    <div class="app-container" style="padding: 10px;">
        <el-form :model="queryParams" ref="queryForm" :inline="true" size="small" label-width="68px">
            <el-form-item label="开始时间">
                <el-date-picker v-model="dateRange" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="点位标签">
                <el-input v-model="queryParams.points" placeholder="多个用逗号分隔"></el-input>
            </el-form-item>
            <el-form-item label="时间间隔">
                <el-input-number v-model="queryParams.timeInterge" :min="1"></el-input-number>
            </el-form-item>
            <el-form-item label="时间间隔">
                <el-select v-model="queryParams.timeType" placeholder="请选择">
                    <el-option v-for="item in timeTypes" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="queryList" icon="el-icon-search">查询</el-button>
            </el-form-item>
        </el-form>
        <el-tabs type="border-card">
            <el-tab-pane label="趋势图">
                <div ref="chart4" class="chart4" style="width: 100%; height: 100%; margin-left: auto; margin-right: auto;">
                </div>
            </el-tab-pane>
            <el-tab-pane label="点位详细值">
                <div class="mes_new_table">
                    <dlTable size="small" :height="dltableHeight" refName="dlTable" :stripe="true" :border="true"
                        :columns="columns" :pageConfig="pageConfig" :tableData="tableData" :basicConfig="basicConfig">
                    </dlTable>
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script>
import dayjs from "dayjs";
import { list } from "@/api/collect/zijinqiao";

export default {
    name: 'ZhiJinQiao',
    data() {
        return {
            chart4: null,
            dltableHeight: 660,
            timeTypes: [{
                value: '秒',
                label: '秒'
            }, {
                value: '分',
                label: '分'
            }, {
                value: '时',
                label: '时'
            }],
            queryParams: {
                timeInterge: 1,
                timeType: '分',
                points: ''
            },
            // 日期范围
            dateRange: [],
            basicConfig: {
                index: false, // 是否启用序号列
                needPage: false, // 是否展示分页
                indexName: null, // 序号列名(默认为：序号)
                selectionType: false, // 是否启用多选框
                indexWidth: 150, // 序号列宽(默认为：50)
                indexFixed: null, // 序号列定位(默认为：left)
                settingType: true, // 是否展示表格配置按钮
                headerSortSaveType: false // 表头排序是否保存在localStorage中
            },
            pageConfig: {
                pageNum: 1, // 页码
                pageSize: 100, // 每页显示条目个数
                total: 0, // 总数
                background: true, // 是否展示分页器背景色
                pageSizes: [100, 200, 500, 1000]// 分页器分页待选项
            },
            columns: [
                {
                    label: '时间',
                    fieldIndex: '时间',
                    width: 150,
                    sortable: true,
                    filterable: true,
                    searchField: null,
                    concise: true,
                },
            ],
            tableData: [],
            option4: {

                tooltip: {
                    trigger: 'axis',
                    confine: true,
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                legend: {
                    data: ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine'],
                    orient: 'horizontal',
                    left: 'left',
                    top: 'top',
                    type: 'scroll',
                },
                dataZoom: [{ // 这部分是dataZoom组件
                    type: 'inside', // 表示内置型数据区域缩放组件
                    start: 0, // 数据窗口范围的起始百分比
                    end: 80 // 数据窗口范围的结束百分比
                }],
                grid: {
                    left: '1%',
                    right: '1%',
                    bottom: '%',
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        boundaryGap: false,
                        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                    }
                ],
                series: [
                    {
                        name: 'Email',
                        type: 'line',
                        stack: 'Total',
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        data: [120, 132, 101, 134, 90, 230, 210]
                    },
                    {
                        name: 'Union Ads',
                        type: 'line',
                        stack: 'Total',
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        data: [220, 182, 191, 234, 290, 330, 310]
                    },
                    {
                        name: 'Video Ads',
                        type: 'line',
                        stack: 'Total',
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        data: [150, 232, 201, 154, 190, 330, 410]
                    },
                    {
                        name: 'Direct',
                        type: 'line',
                        stack: 'Total',
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        data: [320, 332, 301, 334, 390, 330, 320]
                    },
                    {
                        name: 'Search Engine',
                        type: 'line',
                        stack: 'Total',
                        label: {
                            show: true,
                            position: 'top'
                        },
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        data: [820, 932, 901, 934, 1290, 1330, 1320]
                    }
                ]
            },

        }
    },
    created() {
        this.dateRange.push(dayjs(new Date()).startOf("day"))
        this.dateRange.push(dayjs(new Date()));
    },
    methods: {
        queryList() {
            if (this.queryParams.points == '') {
                this.$message.error('请输入点位标签');
                return;
            }
            const obj = JSON.parse(JSON.stringify(this.queryParams))
            if (this.dateRange.length == 2) {
                obj.dtstart = dayjs(this.dateRange[0]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
                obj.dtend = dayjs(this.dateRange[1]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
            }
            obj.points = this.queryParams.points.split(',');
            list(obj).then(res => {
                this.columns = [];
                res.data.table.columns.forEach(element => {
                    this.columns.push({
                        label: element,
                        fieldIndex: element,
                        width: 150,
                        sortable: true,
                        filterable: true,
                        searchField: null,
                        concise: true,
                    });
                });
                this.tableData = res.data.table.data;

                this.chart4 = this.$echarts.init(this.$refs.chart4);
                this.option4.legend.data.splice(0, this.option4.legend.data.length);
                this.option4.xAxis[0].data.splice(0, this.option4.xAxis[0].data.length);
                this.option4.yAxis.splice(0, this.option4.yAxis.length);
                this.option4.series.splice(0, this.option4.series.length);
                res.data.echars.legend.forEach(element => {
                    this.option4.legend.data.push(element);
                    var item = {
                        type: 'value',
                        name: 'SJC_SJ360_T1301',
                        axisLine: {
                            show: true,
                        },
                    };
                    item.name = element;
                    this.option4.yAxis.push(item);
                });

                res.data.echars.xAxis.forEach(element => {
                    if (this.option4.xAxis[0].data.indexOf(element) == -1) {
                        this.option4.xAxis[0].data.push(element)
                    }
                });

                res.data.echars.series.forEach(element => {

                    var optionItem = {
                        name: 'Email',
                        type: 'line',
                        // stack: 'Total',
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        data: []
                    };
                    optionItem.name = element.name;
                    optionItem.data = element.data;
                    this.option4.series.push(optionItem);

                });
                this.chart4.clear();
                this.chart4.setOption(this.option4);
            })
        },
    },
    mounted() {
        this.$nextTick(() => {
            let topValue2 = document.getElementsByClassName('chart4')[0].getBoundingClientRect().top;
            var height = (document.body.clientHeight - topValue2 - 15)
            this.dltableHeight = height;
            document.getElementsByClassName('chart4')[0].style.height = height + 'px';
        })
    },

}
</script>
<style scoped>
/deep/ .el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
    margin-bottom: 10px;
}

/deep/ .el-tabs--border-card>.el-tabs__content {
    padding: 5px;
}
</style>
