<template xmlns:el-col="http://www.w3.org/1999/html">
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="开始时间">
        <el-date-picker v-model="dateRange" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期"
                        type="daterange"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="mainTable.single"
          @click="handleUpdate"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="mainTable.multiple"
          @click="handleDelete"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="queryList"></right-toolbar>
    </el-row>
    <div class="tableInfo">
      <vxe-table
        ref="tableMainRef"
        :data="mainTable.tableData"
        :row-config="{isHover: true}"
        :column-config="{resizable: true}"
        border
        @checkbox-change="mainTableCheckboxChange"
        @checkbox-all="mainTableCheckboxChangeAll"
        header-align="center"
        :span-method="mergeRowsMethod"
        :height="tableHeight-55"
        stripe
      >
        <vxe-column field="check" align="center" fixed="left" type="checkbox" width="60"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.generalInfoId" title="ID" :visible="false" width="auto"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.workDate" title="记账日期" width="auto"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.prodCenterCode" :visible="false" title="加工中心编码" width="auto"
        ></vxe-column>
        <vxe-column field="tCpesGeneralInfo.prodCenterName" :visible="false" title="加工中心名称" width="auto"
        ></vxe-column>
        <vxe-column field="tCpesGeneralInfo.workClsass" title="时间班组" width="auto"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.workGroup" :visible="false" title="管理班组" width="auto"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.productUnitName" title="产品单元名称" width="auto"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.productionFacilityName" title="生产设施名称" width="auto"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.productionFacilityCode" title="生产设施编码" width="auto"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.workDuration" title="当班生产时长" width="auto"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.workStatus" title="当班生产负荷" width="auto"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.productName" title="产品" width="auto"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.productOutput" title="产量(T)" width="auto"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.recordStaff" title="记录人" width="auto"></vxe-column>
        <vxe-column field="mateCode" title="物料编码" width="auto"></vxe-column>
        <vxe-column field="mateName" title="物料名称" width="auto"></vxe-column>
        <vxe-column field="mateCategoryCode" title="物料分类编码" width="auto"></vxe-column>
        <vxe-column field="mateCategoryName" title="物料分类名称" width="auto"></vxe-column>
        <vxe-column field="consumeQty" title="消耗量" width="auto"></vxe-column>
        <vxe-column field="consumeUnit" title="消耗单位" width="auto"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.updateBy" title="修改者" width="auto"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.updateTime" title="修改时间" width="auto"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.createBy" title="创建者" :visible="false" width="auto"></vxe-column>
        <vxe-column field="tCpesGeneralInfo.createTime" title="创建时间" :visible="false" width="auto"></vxe-column>

      </vxe-table>
      <vxe-pager
        :current-page.sync="mainTable.pageConfig.pageNum"
        :page-size.sync="mainTable.pageConfig.pageSize"
        :total="mainTable.pageConfig.total"
        @page-change="mainTablePageChange"
      >
      </vxe-pager>
    </div>
    <!-- 添加或修改生产信息综合管理对话框 -->
    <el-dialog :title="editDialog.title" :visible.sync="editDialog.open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="记账日期" prop="workDate">
              <el-date-picker clearable style="width: 100%;"
                              v-model="form.workDate"
                              type="date"
                              value-format="yyyy-MM-dd"
                              placeholder="请选择记账日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="时间班组" prop="workClsass">
              <BWorkShiftSelect v-model="form.workClsass" model-name="烧结排班"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="管理班组" prop="workGroup">
              <BWorkClassSelect v-model="form.workGroup" model-name="烧结排班"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="产量" prop="productOutput">
              <el-input-number v-model="form.productOutput" :precision="2" :step="0.1" style="width: 100%"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="记录人" prop="recordStaff">
              <el-input v-model="form.recordStaff" placeholder="请输入记录人" style="width: 100%"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="生产时长" prop="workDuration">
              <el-input-number v-model="form.workDuration" :precision="2" :step="0.1" style="width: 100%"
              ></el-input-number>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="生产负荷" prop="workStatus">

              <el-select v-model="queryParams.workStatus" placeholder="请选择当班生产负荷" clearable
                         style="width: 100%"
              >
                <el-option
                  v-for="dict in dict.type.general_info_work_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-tabs type="border-card">
          <el-tab-pane label="物料清单">

            <el-row>
              <el-col :span="24">
                <el-button-group style="float: right; margin-right: 10px;">
                  <el-button icon="el-icon-plus" size="small" type="success" @click="editAddHandler">新增</el-button>
                  <el-button icon="el-icon-delete" size="small" type="danger" @click="editDeleteHandler">删除
                  </el-button>
                </el-button-group>
              </el-col>
            </el-row>

            <el-row style="margin-top: 10px">
              <el-col :span="24">
                <vxe-table
                  ref="editTableef"
                  :column-config="{resizable: true}"
                  :data="form.detailList"
                  :height="450"
                  :row-config="{isHover: true}"
                  border
                  header-align="center"
                  stripe
                >
                  <vxe-column align="center" type="checkbox" width="10%"></vxe-column>
                  <vxe-column field="mateCode" title="物料名称" width="40%">
                    <template #default="{ row }">
                      <BMateSelect v-model="row.mateCode"/>
                    </template>
                  </vxe-column>
                  <vxe-column field="consumeQty" title="消耗量" width="25%">
                    <template #default="{ row }">
                      <el-input-number v-model="row.consumeQty" :precision="3"
                                       style="width: 100%"
                      ></el-input-number>
                    </template>
                  </vxe-column>
                </vxe-table>
              </el-col>
            </el-row>


          </el-tab-pane>

        </el-tabs>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="syncClick">同 步</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { async, delInfo, getInfo, queryForManger, saveOrUpdate } from '@/api/cpes/generalInfo'
import dayjs from 'dayjs'
import BWorkClassSelect from '@/components/BWorkClassSelect/index.vue'
import BWorkShiftSelect from '@/components/BWorkShiftSelect/index.vue'
import BMateSelect from '@/components/BMateSelect/index.vue'

export default {
  name: 'generalInfo',
  components: { BMateSelect, BWorkShiftSelect, BWorkClassSelect },
  dicts: ['general_info_work_status'],
  data() {
    return {
      dateRange: [],
      tableHeight: 300,
      mainTable: {
        loading: true,
        single: true,
        multiple: true,
        tableData: [],
        selectId: [],
        pageConfig: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页显示条目个数
          total: 0, // 总数
          background: true, // 是否展示分页器背景色
          pageSizes: [10, 20, 50, 100]// 分页器分页待选项
        }
      },
      showSearch: true,
      editDialog: {
        title: '',
        // 是否显示弹出层
        open: false
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        prodCenterCode: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        workDate: [
          {
            required: true, message: '记账日期不能为空', trigger: 'blur'
          }
        ],

        workClsass: [
          {
            required: true, message: '时间班组不能为空', trigger: 'blur'
          }
        ],
        workGroup: [
          {
            required: true, message: '管理班组不能为空', trigger: 'blur'
          }
        ],
        productOutput: [
          {
            required: true, message: '产量不能为空', trigger: 'blur'
          }
        ],
        workDuration: [
          {
            required: true, message: '生产时间不能为空', trigger: 'blur'
          }
        ],
        workStatus: [
          {
            required: true, message: '生产负荷不能为空', trigger: 'blur'
          }
        ]
      }
    }
  },
  created() {
    this.dateRange.push(dayjs(new Date()).startOf('date'))
    this.dateRange.push(dayjs(new Date()).endOf('date'))
    this.queryParams.pageNum = this.mainTable.pageConfig.pageNum
    this.queryParams.pageSize = this.mainTable.pageConfig.pageSize
    this.queryList()
  },
  methods: {
    queryList() {
      this.queryParams.pageNum = this.mainTable.pageConfig.pageNum
      this.queryParams.pageSize = this.mainTable.pageConfig.pageSize
      if (this.dateRange.length == 2) {
        this.queryParams.dtStart = dayjs(this.dateRange[0]).startOf('date').format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.queryParams.dtEnd = dayjs(this.dateRange[1]).endOf('date').format(
          'YYYY-MM-DD HH:mm:ss'
        )
      }
      this.queryParams.prodCenterCode = this.getProdCenterCode()
      this.mainTable.tableData = []
      queryForManger(this.queryParams).then(response => {
        this.mainTable.tableData = response.rows
        this.mainTable.pageConfig.total = response.total
      })
    },
    // 取消按钮
    cancel() {
      this.editDialog.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        generalInfoId: null,
        workDate: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null,
        workDuration: null,
        workStatus: '0',
        productName: null,
        productOutput: null,
        recordStaff: null,
        detailList: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.mainTable.pageConfig.pageNum = 1
      this.queryList()
    },
    mainTableCheckboxChange() {
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords()
      this.mainTable.single = selectedRows.length !== 1
      this.mainTable.multiple = !selectedRows.length
    },
    mainTableCheckboxChangeAll() {
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords()
      this.mainTable.single = selectedRows.length !== 1
      this.mainTable.multiple = !selectedRows.length
    },
    mainTablePageChange({ pageSize, currentPage }) {
      this.mainTable.pageConfig.pageNum = currentPage
      this.mainTable.pageConfig.pageSize = pageSize
      this.queryParams.pageNum = this.mainTable.pageConfig.pageNum
      this.queryParams.pageSize = this.mainTable.pageConfig.pageSize
      this.queryList()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.editDialog.open = true
      this.editDialog.title = '添加生产信息综合管理'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords()
      if (selectedRows == null || selectedRows.length == 0) {
        this.$message.warning('请选择一条数据进行修改！')
        return
      }
      if (selectedRows.length != 1) {
        this.$message.warning('同时只能对一掉数据进行修改')
        return
      }
      const generalInfoId = selectedRows[0].generalInfoId
      getInfo(generalInfoId).then(response => {
        this.form = response.data
        this.editDialog.open = true
        this.editDialog.title = '修改生产信息综合管理'
      })
    },
    syncClick() {
      var params = {
        workDate: this.form.workDate,
        prodCenterCode: this.getProdCenterCode(),
        workClsass: this.form.workClsass
      }
      async(params).then(response => {
        console.log(JSON.stringify(response.data))
        this.form.workDuration = response.data.workDuration
        this.form.productOutput = response.data.productOutput
        if (this.form.hasOwnProperty('detailList') === true) {
          response.data.detailList.forEach(item => {
            if (this.form.detailList.filter(x => x.mateCode === item.mateCode).length === 0) {
              this.form.detailList.push({
                detailId: -1 * new Date().getTime() / 1000,
                mateCode: item.mateCode,
                consumeQty: item.consumeQty
              })
            }
          })
        }
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          var params = JSON.parse(JSON.stringify(this.form))
          params.prodCenterCode = this.getProdCenterCode()
          saveOrUpdate(params).then(response => {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.editDialog.open = false
            this.handleQuery()
          })
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords()
      if (selectedRows == null || selectedRows.length == 0) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
        return
      }
      const configIds = selectedRows.map(item => item.generalInfoId)
      this.$modal
        .confirm('是否确认删除？')
        .then(function() {
          return delInfo(configIds)
        })
        .then(() => {
          this.handleQuery()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    },

    /** 导出按钮操作 */
    handleExport() {
      const $table = this.$refs.tableMainRef
      if ($table) {
        $table.exportData({
          type: 'csv',
          filename: '生产信息综合管理', // 文件名
          sheetName: 'Sheet1' // sheet名称
        })
      }
    },
    getProdCenterCode() {
      return this.$route.query.prodCenterCode
    },
    editAddHandler() {
      this.form.detailList.push({
        detailId: -1 * new Date().getTime() / 1000,
        mateCode: null,
        consumeQty: 0
      })
    },
    editDeleteHandler() {
      const selectedRows = this.$refs.editTableef.getCheckboxRecords()
      if (selectedRows == null || selectedRows.length == 0) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
        return
      }
      selectedRows.forEach(item => {
        this.form.detailList.splice(this.form.detailList.indexOf(item), 1)
      })
    },
    mergeRowsMethod({ row, rowIndex, column, data }) {
      const fields = [
        'check',
        'tCpesGeneralInfo.workDate',
        'tCpesGeneralInfo.workClsass',
        'tCpesGeneralInfo.productUnitName',
        'tCpesGeneralInfo.productionFacilityName',
        'tCpesGeneralInfo.productionFacilityCode',
        'tCpesGeneralInfo.workDuration',
        'tCpesGeneralInfo.workStatus',
        'tCpesGeneralInfo.productName',
        'tCpesGeneralInfo.productOutput',
        'tCpesGeneralInfo.recordStaff',
        'tCpesGeneralInfo.updateBy',
        'tCpesGeneralInfo.updateTime'
      ] // 需要合并的字段
      if (fields.includes(column.field)) {
        var count = 0
        for (var i = 0; i < this.mainTable.tableData.length; i++) {
          if (this.mainTable.tableData[i].tCpesGeneralInfo.generalInfoId === row.tCpesGeneralInfo.generalInfoId) {
            count++
          }
        }
        const groupSize = count // 每 8 行合并一次

        const groupIndex = Math.floor(rowIndex / groupSize)
        const firstRowInGroup = groupIndex * groupSize
        if (rowIndex === firstRowInGroup) {
          return { rowspan: groupSize, colspan: 1 }
        } else {
          return { rowspan: 0, colspan: 0 }
        }
      }
      if (column.type === 'radio') { // 若为单选框列

      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      let topValue = document.getElementsByClassName('tableInfo')[0].getBoundingClientRect().top
      this.tableHeight = document.body.clientHeight - topValue - 10

    })
  }
}
</script>
