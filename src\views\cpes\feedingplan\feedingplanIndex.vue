<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form :inline="true" :model="queryParams" class="search-form" size = "small">
        <el-form-item label="加工中心:">
          <el-select v-model="queryParams.prodcode" placeholder="请选择加工中心" clearable style="width: 150px">
            <el-option
              v-for="item in filterOptions.prodOptions"
              :key="item.value"
              :label="item.lable"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="堆号:">
          <el-input v-model="queryParams.planNo" placeholder="请输入堆号" clearable style="width: 150px" />
        </el-form-item>
        <el-form-item label="状态:">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 150px">
            <el-option
              v-for="item in filterOptions.statusOptions"
              :key="item.value"
              :label="item.lable"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="日期:">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button type="primary" @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleAdd">{{ ids  ? '复制' : '新增' }}</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <vxe-table
        ref="xTable"
        :data="tableData"
        :row-config="{isHover: true}"
        :expand-config="{lazy: false}"
        border
        stripe
        align="center"
        size = "small"
        @radio-change="handleRadioChange"
      >
        <vxe-column type="radio" width="30" align="center"></vxe-column>
        <vxe-column type="expand" width="30" align="center">
          <template #content="{ row }">
            <div class="expand-content">
              <!-- 预计烧结矿成分 -->
              <div class="expand-section refined-section">
                <div class="section-header">预计烧结矿成分</div>
                <div class="section-content">
                  <div class="content-items-wrapper">
                    <template v-if="row.sjk && row.sjk[1]">
                      <!-- 化学成分 -->
                      <div class="content-item" v-for="(value, key) in row.sjk[1]" :key="key" v-if="key.startsWith('elem') && value !== undefined && value !== null">
                        <span class="item-label">{{ formatElemName(key) }}</span>
                        <span class="item-value">{{ value }}</span>
                      </div>
                      <!-- 其他指标 -->
                      <div class="content-item">
                        <span class="item-label">碱度</span>
                        <span class="item-value">{{ row.sjk[1].r2 }}</span>
                      </div>
                      <div class="content-item">
                        <span class="item-label">Al/Si</span>
                        <span class="item-value">{{ row.sjk[1].rateAlsi }}</span>
                      </div>
                      <div class="content-item">
                        <span class="item-label">Mg/AL</span>
                        <span class="item-value">{{ row.sjk[1].rateMgal }}</span>
                      </div>
                      <div class="content-item">
                        <span class="item-label">混烧比</span>
                        <span class="item-value">{{ row.sjk[1].rateBurning }}</span>
                      </div>
                      <div class="content-item">
                        <span class="item-label">烧结矿成本</span>
                        <span class="item-value">{{ row.sjk[1].costSjk }}</span>
                      </div>

                    </template>
                    <div class="content-item" v-else>
                      <span class="item-label">暂无数据</span>
                      <span class="item-value">-</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 一次配料 -->
              <div class="expand-section primary-section">
                <div class="section-header">一次配料</div>
                <div class="section-content">
                  <div class="content-items-wrapper">
                    <template v-if="row.list1st && row.list1st.length > 0">
                      <div class="content-item" v-for="(item, index) in row.list1st" :key="index">
                        <span class="item-label">{{ item.materialname || '-' }}</span>
                        <span class="item-value">{{ item.wetRate}}%</span>
                      </div>
                    </template>
                    <div class="content-item" v-else>
                      <span class="item-label">暂无数据</span>
                      <span class="item-value">-</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 二次配料 -->
              <div class="expand-section secondary-section">
                <div class="section-header">二次配料</div>
                <div class="section-content">
                  <div class="content-items-wrapper">
                    <template v-if="row.list2nd && row.list2nd.length > 0">
                      <div class="content-item" v-for="(item, index) in row.list2nd" :key="index">
                        <span class="item-label">{{ item.materialname || '-' }}</span>
                        <span class="item-value">{{ item.wetRate}}%</span>
                      </div>
                    </template>
                    <div class="content-item" v-else>
                      <span class="item-label">暂无数据</span>
                      <span class="item-value">-</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="prodName" title="加工中心名称" ></vxe-column>
        <vxe-column field="planNo" title="堆号" width="120"></vxe-column>
        <vxe-column field="batchWt" title="料批" width="60"></vxe-column>
        <vxe-column field="planExecDate1" title="开堆时间" width="90"></vxe-column>
        <vxe-column field="planExecDate2" title="换堆时间" width="90"></vxe-column>
        <vxe-column field="planRemark" title="备注" show-overflow></vxe-column>
        <vxe-column field="status" title="状态" width="90"></vxe-column>
        <vxe-column field="opuser" title="操作人" ></vxe-column>
        <vxe-column field="optime" title="操作时间" :formatter="formatTime"></vxe-column>
        <vxe-column field="beginWorkdate" title="启用时间" :formatter="formatTime"></vxe-column>
        <vxe-column field="endWorkdate" title="停止时间" :formatter="formatTime"></vxe-column>


        <!-- <vxe-column title="烧结矿信息" align="center">
          <vxe-column field="tfe" title="TFe" width="100"></vxe-column>
          <vxe-column field="basicity" title="碱度" width="100"></vxe-column>
          <vxe-column field="output" title="预计产量" width="120"></vxe-column>
          <vxe-column field="mixRatio" title="混烧比" width="100"></vxe-column>
        </vxe-column> -->
        <vxe-column title="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleRowDelete(row)">删除</el-button>
            <el-button type="text" size="small" @click="handleRowUpdate(row)">详情</el-button>
            <el-button v-if="row.nextNodes && row.nextNodes.length > 0 && row.nextNodes[0]" type="text" size="small" @click="handleRowNext(row)">{{ row.nextNodes[0] }}</el-button>
          </template>
        </vxe-column>
      </vxe-table>

      <!-- 分页 -->
      <vxe-pager
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        :total="total"
        :layouts="['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'Total']"
        :page-sizes="[10, 20, 50, 100]"
        :pager-count="3"
        align="center"
        border
        background
        perfect
        @page-change="handlePageChange"
      ></vxe-pager>
    </div>
    <el-dialog :title="'配料计划详情'" center ref="diaPlanInfo" :visible.sync="dialogPlanInfo" 
    :close-on-press-escape="false"
    v-if="dialogPlanInfo"
      fullscreen append-to-body :close-on-click-modal="false" @close="onDialogClose">
      <planinfo ref="formPlanInfo"
        :planId="planInfoParams.planId"
        :srcPlanId="planInfoParams.srcPlanId"
        :prodCode="planInfoParams.prodCode"
        :dataList2nd="planInfoParams.dataList2nd"
      />
    </el-dialog>

    <el-dialog :title="planCalcParams.srcplanid ? '复制配料计划' : '新增配料计划'"
      center
      :visible.sync="dialogPlanCalc"
      :close-on-press-escape="false"
      v-if="dialogPlanCalc"
      fullscreen
      append-to-body
      :close-on-click-modal="false"
      @close="onPlanCalcDialogClose">
      <planCalc
        :srcplanid="planCalcParams.srcplanid"
        :propProdCode="planCalcParams.propProdCode"
      />
    </el-dialog>
  </div>
</template>

<script>
import {
  listFeedingPlan,//查询配料计划列表
  getFeedingPlan,
  getFeedingPlanDetails,
  delFeedingPlan,

  goNextStep,
  getFilterOptions
} from "@/api/feedingplan/feedingplan";

import planinfo from './formula.vue'
import planCalc from './planCalc.vue'
import XEUtils from 'xe-utils'

export default {
  name: "FeedingPlan",
  components: {
    planinfo,
    planCalc,
  },
  data() {
    // 时间格式
    const formatTime = ({ cellValue }) => {
      return XEUtils.toDateString(cellValue, 'yyyy-MM-dd HH:mm:ss')
    }
    return {
      formatTime,
      // 遮罩层
      loading: false,
      // 选中数组 (单选模式下只会有一个ID)
      ids: null,
      // 非单个禁用 (单选模式下始终为false，表示始终只选中一个)
      single: false,
      // 非多个禁用 (单选模式下始终为false，表示始终有选中项)
      multiple: true,
      // 总条数
      total: 6,
      // 日期范围
      dateRange: [],
      // 表格数据
      tableData: [],
      // 分页加载状态
      pageLoading: false,
      // 弹出层标题
      dialog: {
        title: "",
        visible: false
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        beginworkdate: undefined,
        endworkdate: undefined,
        prodcode: undefined,
        planNo: undefined,
        status: undefined,
        pagekey: null,
        orderByColumn: "createtime", 
        isAsc: "desc",
      },
      // 表单参数
      form: {},
      // 筛选选项
      filterOptions: {
        prodOptions: [], // 加工中心选项
        statusOptions: [] // 状态选项
      },
      dialogPlanInfo: false, // 控制详情弹窗显示
      planInfoParams: {},    // 存放详情参数
      dialogPlanCalc: false, // 控制新增/复制弹窗显示
      planCalcParams: {},    // 存放新增/复制参数
    };
  },
  created() {
    this.getFilterOptions();
    this.getList();
  },
  methods: {
    /** 查询配料计划列表 */
    getList() {
      this.loading = true;
      // 处理日期范围
      let params = { ...this.queryParams };
      if (this.dateRange && this.dateRange.length > 0) {
        params.beginworkdate = this.dateRange[0];
        params.endworkdate = this.dateRange[1];
      }

      // 清空选中数据
      this.ids = null;
      this.single = false;
      this.multiple = true;

      listFeedingPlan(params).then(response => {
        console.log('列表信息',response);
        if (response.code === 200) {
          this.tableData = response.data.rows || [];
          this.total = response.data.total || 0;
          if (response.data.pageNum) {
            this.queryParams.pageNum = response.data.pageNum;
          }
          if (response.data.pageSize) {
            this.queryParams.pageSize = response.data.pageSize;
          }
        } else {
          this.$message.error(response.msg || "获取数据失败");
        }
        this.loading = false;
      }).catch(error => {
        console.error("获取数据出错:", error);

      });
    },

    // 单选框选中数据
    handleRadioChange({ row }) {
      this.ids = row.planid;
      this.single = false;
      this.multiple = false;
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      
      if (this.queryParams.pageNum !== currentPage || this.queryParams.pageSize !== pageSize) {
        this.queryParams.pageNum = currentPage;
        this.queryParams.pageSize = pageSize;

        this.loading = true;

        this.getList();

        // 滚动到页面顶部
        window.scrollTo(0, 0);
      }
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮操作
    handleReset() {
      this.dateRange = [];
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        beginworkdate: undefined,
        endworkdate: undefined,
        prodcode: undefined,
        planNo: undefined,
        status: undefined,
        pagekey: null
      };
      this.getList();
    },
    // 新增\复制按钮操作
    handleAdd() {
      if (this.ids !== null) {
        // 复制
        const selectedRow = this.tableData.find(row => row.planid === this.ids);
        this.planCalcParams = {
          srcplanid: selectedRow.planid,
          propProdCode: selectedRow.prodCode || ''
        };
        this.dialogPlanCalc = true;
      } else {
        // 新增
        this.planCalcParams = {
          srcplanid: '',
          propProdCode: this.queryParams.prodCode || ''
        };
        this.dialogPlanCalc = true;
      }
    },

    // 行内删除按钮操作
    handleRowDelete(row) {
      this.$confirm(`是否确认删除堆号为"${row.planNo}"的记录?`, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        delFeedingPlan(row.planid).then(() => {
          this.$message.success(`删除成功: ${row.planNo}`);
          this.getList();
        }).catch(() => {
          this.$message.error("删除失败，请稍后重试");
        });
      }).catch(() => {});
    },
    // 修改按钮操作（弹窗显示详情）
    handleRowUpdate(row) {
      this.planInfoParams = {
        planId: row.planid,
        srcPlanId: row.srcPlanId || '', // 若有srcPlanId
        prodCode: row.prodCode,
        dataList2nd: row.list2nd || []
      };
      this.dialogPlanInfo = true;
    },
    // 弹窗关闭时刷新主列表
    onDialogClose() {
      this.dialogPlanInfo = false;
      this.getList();
    },
    // 下一步按钮操作
    handleRowNext(row) {
      const nextStep = row.nextNodes && row.nextNodes.length > 0 ? row.nextNodes[0] : '下一步';
      this.$confirm(`确认将堆号 ${row.planNo} 推进到${nextStep}吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info"
      }).then(() => {
        const params = {
          planid: row.planid,
          nodeoption:1
        };

        // 调用下一步接口
        goNextStep(params).then(response => {
          if (response.code === 200) {
            this.$message.success(`堆号 ${row.planNo} 已成功进入${nextStep}`);
            if (response.data) {
              const index = this.tableData.findIndex(item => item.planid === row.planid);
              if (index !== -1) {
                const updatedRow = { ...this.tableData[index] };
                if (response.data.status) {
                  updatedRow.status = response.data.status;
                }
                if (response.data.nextNodes) {
                  updatedRow.nextNodes = response.data.nextNodes;
                }
                // 更新表格中的行
                this.$set(this.tableData, index, updatedRow);
                if (this.$refs.xTable) {
                  this.$refs.xTable.reloadRow(updatedRow);
                }
                this.getList();
              } else {
                this.getList();
              }
            } else {
              // 如果没有返回数据，则重新加载整个列表
              this.getList();
            }
          } else {
            this.$message.error(response.msg || "操作失败");
          }
        }).catch(error => {

          this.$message.error("操作失败", error);
        });
      }).catch(() => {});
    },

    // 格式化元素名称
    formatElemName(key) {
      // 移除 'elem' 前缀
      let name = key.replace('elem', '');

      // 处理元素名称
      const specialElements = {
        'Tfe': 'TFe',
        'Feo': 'FeO',
        'Sio2': 'SiO2',
        'Al2o3': 'Al2O3',
        'Cao': 'CaO',
        'Mgo': 'MgO',
        'Tio2': 'TiO2',
        'H2o': 'H2O',
        'P': 'P',
        'S': 'S',
        'Mn': 'Mn',
        'Zn': 'Zn'
      };

      return specialElements[name] || name;
    },

    // 获取筛选选项
    getFilterOptions() {
      getFilterOptions().then(response => {
        if (response.code === 200 && response.data) {
          // 设置加工中心选项
          if (response.data.prod && Array.isArray(response.data.prod)) {
            this.filterOptions.prodOptions = response.data.prod;
          }

          // 设置状态选项
          if (response.data.status && Array.isArray(response.data.status)) {
            this.filterOptions.statusOptions = response.data.status;
          }
        } else {
          this.$message.warning("获取筛选选项失败");
        }
      }).catch(error => {
        console.error("获取筛选选项出错:", error);
        this.$message.error("获取筛选选项失败，请稍后重试");
      });
    },

    // 重置表单
    reset() {
      this.form = {};
    },

    // 新增/复制弹窗关闭时刷新主列表
    onPlanCalcDialogClose() {
      this.dialogPlanCalc = false;
      this.getList();
    },
  }
};
</script>

<style scoped>
.app-container {
  padding: 10px;
  background-color: #f5f7fa;
}

.search-container {
  background-color: #f5f7fa;
  padding-left: 10px;
  padding-bottom:0px;
  padding-top:16px;
  border-radius: 4px;
  margin-bottom: 15px;
}



.table-container {
  margin-bottom: 20px;
  overflow: visible;
  position: relative;
  z-index: 1; /* 确保比分页组件的z-index低 */
  padding-bottom: 10px;
}

.expand-content {
  padding: 15px;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 展开行样式 */
.expand-section {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0;
}

.section-header {
  padding: 8px 12px;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #dcdfe6;
}

.section-content {
  padding: 10px;
}

.content-items-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.content-item {
  display: flex;
  flex-direction: column;
  padding: 8px 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  min-width: 100px;
  background-color: rgba(255, 255, 255, 0.5);
}

.item-label {
  color: #606266;
  margin-bottom: 4px;
  font-size: 13px;
}

.item-value {
  color: #303133;
  font-weight: 500;
  font-size: 14px;
  text-align: center;
}


.refined-section {
  background-color: #e1f3f8;
}

.refined-section .section-header {
  background-color: #d0ebf6;
}

/* 一次配料样式 */
.primary-section {
  background-color: #fdf6e3;
}

.primary-section .section-header {
  background-color: #f5e9c3;
}

/* 二次配料样式 */
.secondary-section {
  background-color: #e8f4fd;
}

.secondary-section .section-header {
  background-color: #d0e5f7;
}

/* 分页样式 */
.vxe-pager {
  margin-top: 25px;
  margin-bottom: 10px;
  padding: 15px 0;
  background-color: #f5f7fa;
  border-radius: 4px;
  position: relative;
  z-index: 10; /* 提高z-index值 */
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.vxe-pager .vxe-pager--jump-next,
.vxe-pager .vxe-pager--jump-prev {
  color: #409EFF;
}

.vxe-pager .vxe-pager--btn-wrapper .vxe-pager--btn.is--active {
  background-color: #409EFF;
  color: #fff;
}

/* 确保下拉菜单显示在最上层 */
.vxe-select--panel,
.vxe-table--tooltip-wrapper,
.vxe-dropdown--panel,
.el-select-dropdown,
.el-dropdown-menu {
  z-index: 9999 !important;
}

/* 修复下拉菜单被遮挡的问题 */
.vxe-select--panel,
.vxe-dropdown--panel,
.el-select-dropdown,
.el-dropdown-menu {
  overflow: visible !important;
  transform: translateZ(0);
}

/* 确保页面内容不会被遮挡 */
.app-container {
  /* padding-bottom: 80px; */
}
</style>