<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="auto" size="small">
      <el-form-item label="开始时间">
        <el-date-picker v-model="dateRange" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期"
                        type="daterange"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="时间班组" prop="workClsass">
        <BWorkShiftSelect v-model="queryParams.workClsass" model-name="烧结排班"/>
      </el-form-item>
      <el-form-item label="管理班组" prop="workGroup">
        <BWorkClassSelect v-model="queryParams.workGroup" model-name="烧结排班"/>
      </el-form-item>

      <el-form-item>
        <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-edit"
          plain
          size="mini"
          type="success"
          @click="handleUpdate"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-delete"
          plain
          size="mini"
          type="danger"
          @click="handleDelete"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="queryList"></right-toolbar>
    </el-row>

    <div class="tableInfo">
      <vxe-grid
        ref="tableMainRef"
        :column-config="{resizable: true}"
        v-bind="mainTable.gridOptions"
        :row-style="tableRowStyle"
        @radio-change="handleRadioChange"
        :span-method="mergeRowsMethod"
      >
      </vxe-grid>


    </div>

    <!-- 添加或修改烧结物料消耗对话框 -->
    <el-dialog :title="editDialog.title" :visible.sync="editDialog.open" append-to-body width="600">

      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>

          <el-col :span="12">
            <el-form-item label="管理班组" prop="workGroup">
              <BWorkClassSelect v-model="form.workGroup" model-name="烧结排班"/>

            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="时间班组" prop="workClsass">
              <BWorkShiftSelect v-model="form.workClsass" model-name="烧结排班"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="beginTime">
              <el-date-picker v-model="form.beginTime"
                              clearable style="width: 100%"
                              placeholder="请选择记账日期"
                              type="datetime"
                              value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker v-model="form.endTime"
                              clearable style="width: 100%"
                              placeholder="请选择记账日期"
                              type="datetime"
                              value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-tabs type="border-card">
          <el-tab-pane label="仓位清单">
            <el-row v-if="form.isAdd===true">
              <el-col :span="20">
                <el-select style="width: 100%" size="small" multiple v-model="form.selectPostions"
                           placeholder="请选择仓位信息"
                >
                  <el-option
                    v-for="item in form.postionList"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
              </el-col>

              <el-col :span="4">
                <el-button icon="el-icon-refresh" size="small" type="success" @click="editsyncHandler">采集信息
                </el-button>
              </el-col>
            </el-row>

            <el-row style="margin-top: 10px">
              <el-col :span="24">
                <div class="tableInfo">
                  <vxe-grid ref="gridRef" v-bind="editTable.gridOptions">

                  </vxe-grid>

                </div>
              </el-col>
            </el-row>


          </el-tab-pane>

        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {
  deleteForManger,
  getEdit,
  getpostionList,
  queryForManger,
  saveOrUpdate,
  sync
} from '@/api/cpes/postionCalibration'
import dayjs from 'dayjs'
import BWorkClassSelect from '@/components/BWorkClassSelect/index.vue'
import BWorkShiftSelect from '@/components/BWorkShiftSelect/index.vue'
import BMateSelect from '@/components/BMateSelect/index.vue'

export default {
  name: 'ConsumeManger',
  components: { BMateSelect, BWorkShiftSelect, BWorkClassSelect },
  data() {
    return {
      tableHeight: 300,
      editSelectPostionList: [],
      dateRange: [],
      editTable: {
        loading: true,
        single: true,
        multiple: true,
        details: [],
        gridOptions: {
          border: true,
          stripe: true,
          loading: false,
          height: 300,
          columnConfig: {
            resizable: true
          },
          rowConfig: {
            isHover: true
          },
          editConfig: {
            trigger: 'click',
            mode: 'row'
          },
          radioConfig: {
            labelField: 'name',
            trigger: 'row'
          },
          columns: [],
          data: []
        }
      },
      mainTable: {
        loading: true,
        single: true,
        multiple: true,
        selectedRadioRow: null,
        gridOptions: {
          border: true,
          // stripe: true,
          loading: false,
          height: 300,
          columnConfig: {
            resizable: true
          },
          cellConfig: {
            height: 35
          },
          rowConfig: {
            isHover: true
          },
          radioConfig: {
            labelField: 'name',
            trigger: 'row',
            highlight: true
          },
          columns: [
            { type: 'checkbox', width: 60 }
          ],
          data: []
        }
      },
      showSearch: true,
      editDialog: {
        title: '',
        // 是否显示弹出层
        open: false
      },
      // 查询参数
      queryParams: {
        dtStart: null,
        dtEnd: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null
      },
      // 表单参数
      form: {
        workDate: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null,
        postionList: [],
        details: []
      },
      // 表单校验
      rules: {

        workClsass: [
          {
            required: true, message: '时间班组不能为空', trigger: 'blur'
          }
        ],
        workGroup: [
          {
            required: true, message: '管理班组不能为空', trigger: 'blur'
          }
        ],

        beginTime: [
          {
            required: true, message: '开始时间不能为空', trigger: 'blur'
          }
        ],
        endTime: [
          {
            required: true, message: '结束时间不能为空', trigger: 'blur'
          }
        ]

      }
    }
  },
  created() {
    this.dateRange.push(dayjs(new Date()).startOf('date'))
    this.dateRange.push(dayjs(new Date()).endOf('date'))
    this.queryList()
  },
  methods: {
    queryList() {
      if (this.dateRange.length == 2) {
        this.queryParams.dtStart = dayjs(this.dateRange[0]).startOf('date').format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.queryParams.dtEnd = dayjs(this.dateRange[1]).endOf('date').format(
          'YYYY-MM-DD HH:mm:ss'
        )
      }
      this.queryParams.prodCenterCode = this.getProdCenterCode()
      this.mainTable.gridOptions.columns = []
      this.mainTable.gridOptions.data = []
      this.mainTable.selectedRadioRow = null
      queryForManger(this.queryParams).then(response => {
        this.mainTable.gridOptions.columns.push({ type: 'radio', field: 'radio', width: 'auto', fixed: 'left' })

        response.data.col.forEach((item) => {
          if (item.key === 'prodCenterCode'
            || item.key === 'workGroup'
            || item.key === 'workClsass'
            || item.key === 'beginTime'
            || item.key === 'endTime') {
            this.mainTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              width: 'auto',
              visible: false
            })
          } else if (item.key === 'ShowType') {
            this.mainTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              fixed: 'left',
              width: 'auto'
            })
          } else {
            this.mainTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              width: 'auto'
            })
          }

        })
        this.mainTable.gridOptions.data = response.data.rec
      })
    },
    getProdCenterCode() {
      return this.$route.query.prodCenterCode
    },
    // 取消按钮
    cancel() {
      this.editDialog.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        isAdd: true,
        beginTime: null,
        endTime: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null,
        selectPostions: [],
        postionList: [],
        details: []
      }
      this.editTable.gridOptions.columns = []
      this.editTable.gridOptions.data = []
      this.editTable.details = []
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryList()
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      getpostionList(this.getProdCenterCode()).then(response => {
        this.form.postionList = response.data
        this.editDialog.open = true
        this.editDialog.title = '添加仓位校准记录'
      })

    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const selectedRows = this.mainTable.selectedRadioRow
      if (selectedRows == null) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
      }
      const queryPar = {
        prodCenterCode: selectedRows.prodCenterCode,
        dtStart: selectedRows.beginTime,
        dtEnd: selectedRows.endTime,
        workClsass: selectedRows.workClsass,
        workGroup: selectedRows.workGroup
      }
      this.reset()
      this.form.isAdd = false
      getEdit(queryPar).then(response => {
        const data = response.data

        this.editTable.details = data.detail
        this.form.workClsass = data.detail[0].workClsass
        this.form.workGroup = data.detail[0].workGroup
        this.form.beginTime = data.detail[0].beginTime
        this.form.endTime = data.detail[0].endTime

        response.data.col.forEach((item) => {
          if (item.key == 'ShowType') {
            this.editTable.gridOptions.columns.push({
              field: item.key,
              title: item.value
            })
          } else {
            this.editTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              editRender: { name: 'VxeNumberInput' }
            })
          }

        })
        this.editTable.gridOptions.data = response.data.rec
        this.editDialog.open = true
        this.editDialog.title = '修改仓位校准记录'
      })

    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.editTable.gridOptions.data.length === 0) {
            this.$message({
              message: '请填写采集物料信息',
              type: 'warning'
            })
            return
          }

          var storageNumberProperties = this.editTable.gridOptions.data.reduce((acc, obj) => {
            Object.keys(obj).forEach(key => {
              if (key.startsWith('storageNumber_')) {
                acc.push(key.replace('storageNumber_', ''))
              }
            })
            return acc
          }, [])
          storageNumberProperties = storageNumberProperties.filter((item, index) => {
            return storageNumberProperties.indexOf(item) === index
          })
          var saveList = []
          var dataList = JSON.parse(JSON.stringify(this.editTable.gridOptions.data))
          for (var i = 0; i < storageNumberProperties.length; i++) {
            var item = storageNumberProperties[i]

            /* 仓位 */
            {
              var saveEntity = {}
              saveEntity.prodCenterCode = this.getProdCenterCode()
              saveEntity.workClsass = this.form.workClsass
              saveEntity.workGroup = this.form.workGroup
              saveEntity.beginTime = this.form.beginTime
              saveEntity.endTime = this.form.endTime
              saveEntity.storageNumber = storageNumberProperties[i]
              saveEntity.operType = '仓位'
              saveEntity.beginValue = 0
              saveEntity.endValue = 0

              var matchedRecords = dataList.filter((record) => {
                return record.ShowType === '开始仓位'
              })
              if (matchedRecords.length > 0) {
                const field = 'storageNumber_' + saveEntity.storageNumber
                saveEntity.beginValue = matchedRecords[0][field]
              }

              matchedRecords = dataList.filter((record) => {
                return record.ShowType === '结束仓位'
              })
              if (matchedRecords.length > 0) {
                const field = 'storageNumber_' + saveEntity.storageNumber
                saveEntity.endValue = matchedRecords[0][field]
              }

              if (this.editTable.details != null && this.editTable.details.length > 0) {
                const matchedDetail = this.editTable.details.find(detail => {
                  return (
                    detail.prodCenterCode === saveEntity.prodCenterCode &&
                    detail.workClsass === saveEntity.workClsass &&
                    detail.workGroup === saveEntity.workGroup &&
                    detail.beginTime === saveEntity.beginTime &&
                    detail.endTime === saveEntity.endTime &&
                    detail.storageNumber === saveEntity.storageNumber &&
                    detail.operType === saveEntity.operType
                  )
                })
                if (matchedDetail) {
                  saveEntity.calibrationId = matchedDetail.calibrationId
                }
              }

              saveList.push(saveEntity)

            }

            /* 流量 */
            {
              var saveEntity = {}
              saveEntity.prodCenterCode = this.getProdCenterCode()
              saveEntity.workClsass = this.form.workClsass
              saveEntity.workGroup = this.form.workGroup
              saveEntity.workDate = this.form.workDate
              saveEntity.beginTime = this.form.beginTime
              saveEntity.endTime = this.form.endTime
              saveEntity.storageNumber = storageNumberProperties[i]
              saveEntity.operType = '流量'
              saveEntity.beginValue = 0
              saveEntity.endValue = 0

              var matchedRecords = dataList.filter((record) => {
                return record.ShowType === '设定流量'
              })
              if (matchedRecords.length > 0) {
                const field = 'storageNumber_' + saveEntity.storageNumber
                saveEntity.beginValue = matchedRecords[0][field]
              }

              matchedRecords = dataList.filter((record) => {
                return record.ShowType === '平均流量'
              })
              if (matchedRecords.length > 0) {
                const field = 'storageNumber_' + saveEntity.storageNumber
                saveEntity.endValue = matchedRecords[0][field]
              }

              if (this.editTable.details != null && this.editTable.details.length > 0) {
                const matchedDetail = this.editTable.details.find(detail => {
                  return (
                    detail.prodCenterCode === saveEntity.prodCenterCode &&
                    detail.workClsass === saveEntity.workClsass &&
                    detail.workGroup === saveEntity.workGroup &&
                    detail.beginTime === saveEntity.beginTime &&
                    detail.endTime === saveEntity.endTime &&
                    detail.storageNumber === saveEntity.storageNumber &&
                    detail.operType === saveEntity.operType
                  )
                })
                if (matchedDetail) {
                  saveEntity.calibrationId = matchedDetail.calibrationId
                }
              }

              saveList.push(saveEntity)

            }
          }
          saveOrUpdate(saveList).then(response => {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.editDialog.open = false
            this.handleQuery()
          })
        }

      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const selectedRows = this.mainTable.selectedRadioRow
      if (selectedRows == null) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
      }
      const deletePar = {
        prodCenterCode: selectedRows.prodCenterCode,
        dtStart: selectedRows.beginTime,
        dtEnd: selectedRows.endTime,
        workClsass: selectedRows.workClsass,
        workGroup: selectedRows.workGroup
      }

      this.$modal
        .confirm('是否确认删除？')
        .then(function() {
          return deleteForManger(deletePar)
        })
        .then(() => {
          this.handleQuery()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    },

    /** 导出按钮操作 */
    handleExport() {
      const $table = this.$refs.tableMainRef
      if ($table) {
        $table.exportData({
          type: 'csv',
          filename: '仓位校准记录', // 文件名
          sheetName: 'Sheet1' // sheet名称
        })
      }
    },
    handleRadioChange({ row }) {
      this.mainTable.selectedRadioRow = row
    },
    editsyncHandler() {
      if (this.form.postionList.length == 0) {
        this.$message({
          message: '请选择仓位信息',
          type: 'warning'
        })
        return
      }
      if (this.form.beginTime == null || this.form.endTime == null) {
        this.$message({
          message: '请选择时间',
          type: 'warning'
        })
        return
      }
      var parm = {
        prodCenterCode: this.getProdCenterCode(),
        storageNumberList: this.form.selectPostions,
        dtStart: this.form.beginTime,
        dtEnd: this.form.endTime
      }
      this.editTable.gridOptions.columns = []
      this.editTable.gridOptions.data = []
      sync(parm).then(response => {
        response.data.col.forEach((item) => {
          if (item.key == 'ShowType') {
            this.editTable.gridOptions.columns.push({
              field: item.key,
              title: item.value
            })
          } else {
            this.editTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              editRender: { name: 'VxeNumberInput' }
            })
          }

        })
        this.editTable.gridOptions.data = response.data.rec
      })

    },
    tableRowStyle({ row }) {
      if (row.ShowType === '时间班组') {
        return { backgroundColor: '#71c9ce' }
      }
    },
    mergeRowsMethod({ row, rowIndex, column, data }) {
      if (column.type === 'radio') { // 若为单选框列
        const groupSize = 8 // 每 8 行合并一次
        const groupIndex = Math.floor(rowIndex / groupSize)
        const firstRowInGroup = groupIndex * groupSize
        if (rowIndex === firstRowInGroup) {
          return { rowspan: groupSize, colspan: 1 }
        } else {
          return { rowspan: 0, colspan: 0 }
        }
      }
      return { rowspan: 1, colspan: 1 }
    }

  },
  mounted() {
    this.$nextTick(() => {
      let topValue = document.getElementsByClassName('tableInfo')[0].getBoundingClientRect().top
      this.tableHeight = document.body.clientHeight - topValue - 10
      this.mainTable.gridOptions.height = this.tableHeight - 5
    })
  }
}
</script>
<style lang="scss" scoped>
.active-row {
  background-color: #c8e6c9; /* 例如，浅绿色背景 */
}

.inactive-row {
  background-color: #ffcdd2; /* 例如，浅红色背景 */
}
</style>
